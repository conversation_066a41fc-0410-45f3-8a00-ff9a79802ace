# CCIP Tools - Windows 最终解决方案

## 🚨 问题总结

在Windows PowerShell中使用CCIP工具时遇到的问题：
1. **字符编码问题**: 批处理文件中的中文注释导致乱码
2. **PowerShell执行策略**: 需要使用 `.\` 前缀
3. **依赖问题**: tsx可能未正确安装或配置

## ✅ 最终解决方案

### 方案1: 直接使用npx (推荐)

这是最可靠的方法，直接使用npx执行：

```powershell
# 查看所有支持的网络
npx tsx src\index.ts list-networks

# 查看特定网络的支持代币
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens polygon --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens sepolia --yaml-config ..\..\..\config\rpc.yaml

# 发送跨链消息
npx tsx src\index.ts send ethereum 0xRouter polygon --receiver 0xAddress --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts send sepolia 0xRouter fuji --receiver 0xAddress --data "Hello" --yaml-config ..\..\..\config\rpc.yaml

# 查看消息状态
npx tsx src\index.ts show 0x1234567890abcdef... --yaml-config ..\..\..\config\rpc.yaml

# 手动执行消息
npx tsx src\index.ts manualExec 0x1234567890abcdef... --yaml-config ..\..\..\config\rpc.yaml
```

### 方案2: 使用改进的PowerShell脚本

```powershell
# 使用最终版本的PowerShell脚本
.\ccip-final.ps1 list-networks
.\ccip-final.ps1 getSupportedTokens ethereum
.\ccip-final.ps1 send ethereum 0xRouter polygon --receiver 0xAddress
```

### 方案3: 创建PowerShell别名

在PowerShell配置文件中添加别名：

```powershell
# 编辑PowerShell配置文件
notepad $PROFILE

# 添加以下内容：
function ccip {
    param([Parameter(ValueFromRemainingArguments=$true)][string[]]$Arguments)
    $configPath = "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\config\rpc.yaml"
    $scriptPath = "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts\src\index.ts"
    
    if ($Arguments.Count -eq 0) {
        Write-Host "Usage: ccip <command> [options]"
        Write-Host "Example: ccip list-networks"
        return
    }
    
    $allArgs = $Arguments + @("--yaml-config", $configPath)
    & npx tsx $scriptPath @allArgs
}

# 重新加载配置
. $PROFILE

# 然后可以直接使用
ccip list-networks
ccip getSupportedTokens ethereum
```

## 🌐 支持的网络名称

### 主网络
```powershell
# Ethereum Mainnet (Chain ID: 1)
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens eth --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens mainnet --yaml-config ..\..\..\config\rpc.yaml

# Polygon Mainnet (Chain ID: 137)
npx tsx src\index.ts getSupportedTokens polygon --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens matic --yaml-config ..\..\..\config\rpc.yaml

# BSC Mainnet (Chain ID: 56)
npx tsx src\index.ts getSupportedTokens bsc --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens bnb --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens binance --yaml-config ..\..\..\config\rpc.yaml

# Avalanche Mainnet (Chain ID: 43114)
npx tsx src\index.ts getSupportedTokens avalanche --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens avax --yaml-config ..\..\..\config\rpc.yaml
```

### 测试网络
```powershell
# Ethereum Sepolia (Chain ID: 11155111)
npx tsx src\index.ts getSupportedTokens sepolia --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens eth-sepolia --yaml-config ..\..\..\config\rpc.yaml

# Avalanche Fuji (Chain ID: 43113)
npx tsx src\index.ts getSupportedTokens fuji --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens avax-fuji --yaml-config ..\..\..\config\rpc.yaml

# Polygon Amoy (Chain ID: 80002)
npx tsx src\index.ts getSupportedTokens amoy --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens polygon-amoy --yaml-config ..\..\..\config\rpc.yaml
```

## 📋 完整的工作流程示例

### 场景: 从Ethereum Sepolia发送消息到Avalanche Fuji

```powershell
# 1. 进入正确的目录
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts

# 2. 查看所有支持的网络
npx tsx src\index.ts list-networks

# 3. 查看Sepolia支持的代币
npx tsx src\index.ts getSupportedTokens sepolia --yaml-config ..\..\..\config\rpc.yaml

# 4. 查看Fuji支持的代币
npx tsx src\index.ts getSupportedTokens fuji --yaml-config ..\..\..\config\rpc.yaml

# 5. 发送跨链消息 (示例地址，请替换为实际地址)
npx tsx src\index.ts send sepolia ****************************************** fuji --receiver 0xYourReceiverAddress --data "Hello from Sepolia to Fuji" --yaml-config ..\..\..\config\rpc.yaml

# 6. 查看消息状态 (使用返回的交易哈希)
npx tsx src\index.ts show 0xYourTransactionHash --yaml-config ..\..\..\config\rpc.yaml
```

## 🛠️ 故障排除

### 问题1: tsx命令不存在
```
'tsx' 不是内部或外部命令
```

**解决方案**:
```powershell
# 全局安装tsx
npm install -g tsx

# 或者使用npx
npx tsx src\index.ts <command>
```

### 问题2: 配置文件未找到
```
Configuration file not found
```

**解决方案**:
```powershell
# 检查配置文件是否存在
Test-Path ..\..\..\config\rpc.yaml

# 如果不存在，创建配置文件或使用绝对路径
npx tsx src\index.ts <command> --yaml-config "C:\full\path\to\config\rpc.yaml"
```

### 问题3: 网络名称无效
```
Invalid source network: "etherem"
```

**解决方案**:
```powershell
# 查看所有支持的网络名称
npx tsx src\index.ts list-networks

# 使用正确的网络名称
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
```

## 💡 实用技巧

### 1. 创建批处理文件快捷方式
创建 `ccip-quick.bat` 文件：
```batch
@echo off
cd /d "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts"
npx tsx src\index.ts %* --yaml-config ..\..\..\config\rpc.yaml
```

然后可以从任何地方使用：
```cmd
ccip-quick.bat list-networks
ccip-quick.bat getSupportedTokens ethereum
```

### 2. 保存常用命令
创建 `my-ccip-commands.txt` 文件保存常用命令：
```
npx tsx src\index.ts list-networks
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts send sepolia ****************************************** fuji --receiver 0xYourAddress --yaml-config ..\..\..\config\rpc.yaml
```

### 3. 使用环境变量
```powershell
# 设置环境变量
$env:CCIP_CONFIG = "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\config\rpc.yaml"
$env:CCIP_SCRIPT = "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts\src\index.ts"

# 然后使用
npx tsx $env:CCIP_SCRIPT list-networks --yaml-config $env:CCIP_CONFIG
```

## 🎯 推荐的最终使用方法

**对于日常使用，推荐使用方案1（直接npx）**：

```powershell
# 设置工作目录
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts

# 使用完整命令
npx tsx src\index.ts list-networks
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts send ethereum 0xRouter polygon --receiver 0xAddress --yaml-config ..\..\..\config\rpc.yaml
```

这种方法最可靠，避免了所有编码和依赖问题。

## ✅ 验证功能

要验证网络名称功能是否正常工作：

```powershell
# 测试网络名称解析
npx tsx src\index.ts list-networks

# 测试不同的网络名称
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens eth --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens polygon --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts getSupportedTokens matic --yaml-config ..\..\..\config\rpc.yaml

# 如果这些命令成功执行，说明网络名称功能正常工作！
```

现在您可以在Windows上使用友好的网络名称来操作CCIP跨链功能了！
