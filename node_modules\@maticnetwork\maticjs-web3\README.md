[![npm version](https://badge.fury.io/js/@maticnetwork%2Fmaticjs-web3.svg)](https://badge.fury.io/js/@maticnetwork%2Fmaticjs-web3)
# maticjs-web3

[web3.js](https://web3js.readthedocs.io/) plugin for matic.js

# Installation

```
npm i @maticnetwork/maticjs-web3
```

# Examples

All the examples are in the examples folder.

# Docs

```

import { use } from '@maticnetwork/maticjs'
import { Web3ClientPlugin } from '@maticnetwork/maticjs-web3'

// install ethers plugin
use(Web3ClientPlugin)
```

That's all you need to do and `web3.js` will be used for web3 calls.




