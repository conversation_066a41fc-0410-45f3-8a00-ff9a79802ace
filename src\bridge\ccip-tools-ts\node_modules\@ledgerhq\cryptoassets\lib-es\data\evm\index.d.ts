import { ERC20Token } from "../../types";
export declare const tokens: {
    1: ERC20Token[];
    10: ERC20Token[];
    14: ERC20Token[];
    19: ERC20Token[];
    25: ERC20Token[];
    30: ERC20Token[];
    40: ERC20Token[];
    56: ERC20Token[];
    57: ERC20Token[];
    106: ERC20Token[];
    137: ERC20Token[];
    146: ERC20Token[];
    199: ERC20Token[];
    246: ERC20Token[];
    250: ERC20Token[];
    324: ERC20Token[];
    592: ERC20Token[];
    1088: ERC20Token[];
    1101: ERC20Token[];
    1284: ERC20Token[];
    1442: ERC20Token[];
    8217: ERC20Token[];
    8453: ERC20Token[];
    42161: ERC20Token[];
    42793: ERC20Token[];
    43114: ERC20Token[];
    59144: ERC20Token[];
    81457: ERC20Token[];
    84532: ERC20Token[];
    421614: ERC20Token[];
    534351: ERC20Token[];
    534352: ERC20Token[];
    11155111: ERC20Token[];
    11155420: ERC20Token[];
    168587773: ERC20Token[];
    245022934: ERC20Token[];
};
export declare const signatures: {
    1: string;
    10: string;
    14: string;
    19: string;
    25: string;
    30: string;
    40: string;
    56: string;
    57: string;
    106: string;
    137: string;
    146: string;
    199: string;
    246: string;
    250: string;
    324: string;
    592: string;
    1088: string;
    1101: string;
    1284: string;
    1442: string;
    8217: string;
    8453: string;
    42161: string;
    42793: string;
    43114: string;
    59144: string;
    81457: string;
    84532: string;
    421614: string;
    534351: string;
    534352: string;
    11155111: string;
    11155420: string;
    168587773: string;
    245022934: string;
};
export declare const hashes: {
    1: string;
    10: string;
    14: string;
    19: string;
    25: string;
    30: string;
    40: string;
    56: string;
    57: string;
    106: string;
    137: string;
    146: string;
    199: string;
    246: string;
    250: string;
    324: string;
    592: string;
    1088: string;
    1101: string;
    1284: string;
    1442: string;
    8217: string;
    8453: string;
    42161: string;
    42793: string;
    43114: string;
    59144: string;
    81457: string;
    84532: string;
    421614: string;
    534351: string;
    534352: string;
    11155111: string;
    11155420: string;
    168587773: string;
    245022934: string;
};
declare const _default: {
    tokens: {
        1: ERC20Token[];
        10: ERC20Token[];
        14: ERC20Token[];
        19: ERC20Token[];
        25: ERC20Token[];
        30: ERC20Token[];
        40: ERC20Token[];
        56: ERC20Token[];
        57: ERC20Token[];
        106: ERC20Token[];
        137: ERC20Token[];
        146: ERC20Token[];
        199: ERC20Token[];
        246: ERC20Token[];
        250: ERC20Token[];
        324: ERC20Token[];
        592: ERC20Token[];
        1088: ERC20Token[];
        1101: ERC20Token[];
        1284: ERC20Token[];
        1442: ERC20Token[];
        8217: ERC20Token[];
        8453: ERC20Token[];
        42161: ERC20Token[];
        42793: ERC20Token[];
        43114: ERC20Token[];
        59144: ERC20Token[];
        81457: ERC20Token[];
        84532: ERC20Token[];
        421614: ERC20Token[];
        534351: ERC20Token[];
        534352: ERC20Token[];
        11155111: ERC20Token[];
        11155420: ERC20Token[];
        168587773: ERC20Token[];
        245022934: ERC20Token[];
    };
    signatures: {
        1: string;
        10: string;
        14: string;
        19: string;
        25: string;
        30: string;
        40: string;
        56: string;
        57: string;
        106: string;
        137: string;
        146: string;
        199: string;
        246: string;
        250: string;
        324: string;
        592: string;
        1088: string;
        1101: string;
        1284: string;
        1442: string;
        8217: string;
        8453: string;
        42161: string;
        42793: string;
        43114: string;
        59144: string;
        81457: string;
        84532: string;
        421614: string;
        534351: string;
        534352: string;
        11155111: string;
        11155420: string;
        168587773: string;
        245022934: string;
    };
    hashes: {
        1: string;
        10: string;
        14: string;
        19: string;
        25: string;
        30: string;
        40: string;
        56: string;
        57: string;
        106: string;
        137: string;
        146: string;
        199: string;
        246: string;
        250: string;
        324: string;
        592: string;
        1088: string;
        1101: string;
        1284: string;
        1442: string;
        8217: string;
        8453: string;
        42161: string;
        42793: string;
        43114: string;
        59144: string;
        81457: string;
        84532: string;
        421614: string;
        534351: string;
        534352: string;
        11155111: string;
        11155420: string;
        168587773: string;
        245022934: string;
    };
};
export default _default;
//# sourceMappingURL=index.d.ts.map