{"name": "@maticnetwork/maticjs", "version": "3.9.2", "description": "Javascript developer library for interacting with Matic Network", "main": "dist/npm.export.js", "types": "dist/ts/index.d.ts", "browser": "dist/matic.umd.js", "react-native": "dist/matic.node.js", "files": ["lib", "dist", "artifacts", "types"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build:link": "webpack && npm link", "build:dev": "webpack", "build:prod": "webpack --env build", "deploy": "npm run clean && npm run build:dev && npm run build:prod", "prepublishOnly": "npm run deploy", "lint": "tslint src/**/*.ts", "lint:fix": "tslint src/**/*.ts --fix", "debug": "webpack && cd test && npm run link:lib:debug"}, "repository": {"type": "git", "url": "git+https://github.com/maticnetwork/matic.js.git"}, "keywords": ["ethereum", "web3", "ethers", "matic"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/maticnetwork/matic.js/issues"}, "homepage": "https://github.com/maticnetwork/matic.js#readme", "dependencies": {"@ethereumjs/block": "^5.2.0", "@ethereumjs/trie": "^6.2.0", "@ethereumjs/util": "^9.0.3", "assert": "^2.1.0", "bn.js": "^5.2.1", "buffer": "^6.0.3", "ethereum-cryptography": "^2.2.1", "node-fetch": "^2.6.1", "rlp": "^3.0.0", "stream": "^0.0.3"}, "devDependencies": {"@types/node-fetch": "^2.6.11", "@typescript-eslint/parser": "^7.3.1", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "husky": "^9.0.11", "lint-staged": "^15.2.2", "ts-loader": "^8.0.0", "tslint": "^6.1.3", "typescript": "^4.9.5", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "yargs": "^17.7.2"}, "browserslist": ["> 1%", "node 8", "not dead"], "engines": {"node": ">=8.0.0"}, "husky": {"hooks": {"pre-commit": "npm run build"}}}