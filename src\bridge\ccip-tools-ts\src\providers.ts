import { readFile } from 'node:fs/promises'
import { resolve } from 'node:path'
import { parse } from 'yaml'

import {
  type JsonRpcApiProvider,
  type TransactionReceipt,
  JsonRpcProvider,
  WebSocketProvider,
} from 'ethers'

import { chainNameFromId, getProviderNetwork } from './lib/index.ts'

const RPCS_RE = /\b(?:http|ws)s?:\/\/[\w/\\@&?%~#.,;:=+-]+/

// 网络名称映射到链ID
const NETWORK_CHAIN_ID_MAP: Record<string, number> = {
  'ethereum': 1,
  'polygon': 137,
  'bsc': 56,
  'avalanche': 43114,
  'arbitrum': 42161,
  'optimism': 10,
  'base': 8453,
  'astar': 592,
  'ronin': 2020,
  'ethereum-mainnet': 1,
  'polygon-mainnet': 137,
  'binance_smart_chain-mainnet': 56,
  'avalanche-mainnet': 43114,
  'ethereum-mainnet-arbitrum-1': 42161,
  'ethereum-mainnet-optimism-1': 10,
  'ethereum-mainnet-base-1': 8453,
  'polkadot-mainnet-astar': 592,
  'ronin-mainnet': 2020,
  // 测试网
  'ethereum-testnet-sepolia': 11155111,
  'polygon-testnet-amoy': 80002,
  'binance_smart_chain-testnet': 97,
  'avalanche-testnet-fuji': 43113,
  'ethereum-testnet-sepolia-arbitrum-1': 421614,
  'ethereum-testnet-sepolia-optimism-1': 11155420,
  'ethereum-testnet-sepolia-base-1': 84532,
}

// RPC配置接口
interface RpcConfig {
  rpc_url?: string
  endpoint?: string
  backup_rpc_urls?: string[]
  backup_endpoints?: string[]
}

interface YamlConfig {
  rpc?: Record<string, RpcConfig>
}

/**
 * Wrap a promise with a timeout
 * @param promise - promise to wrap
 * @param ms - timeout in milliseconds
 * @param message - error message to throw on timeout
 * @param cancel - optional promise to cancel the timeout
 * @returns Promise that resolves when the original promise resolves, or rejects on timeout
 **/
async function withTimeout<T>(
  promise: Promise<T>,
  ms: number,
  message = 'Timeout',
  cancel?: Promise<unknown>,
): Promise<T> {
  let timeoutId: ReturnType<typeof setTimeout> | undefined
  return Promise.race([
    new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => reject(new Error(message)), ms)
    }),
    promise,
    ...(cancel ? [cancel.then(() => Promise.reject(new Error('Cancelled')))] : []),
  ]).finally(() => clearTimeout(timeoutId))
}

/**
 * 从YAML配置文件加载RPC端点
 */
async function loadRpcFromYaml(yamlPath: string): Promise<Set<string>> {
  try {
    const yamlContent = await readFile(yamlPath, 'utf8')
    const config: YamlConfig = parse(yamlContent)
    const rpcs = new Set<string>()

    if (config.rpc) {
      for (const [networkName, rpcConfig] of Object.entries(config.rpc)) {
        // 添加主要RPC端点
        if (rpcConfig.rpc_url) {
          rpcs.add(rpcConfig.rpc_url)
        }
        if (rpcConfig.endpoint) {
          rpcs.add(rpcConfig.endpoint)
        }

        // 添加备用RPC端点
        if (rpcConfig.backup_rpc_urls) {
          rpcConfig.backup_rpc_urls.forEach(url => rpcs.add(url))
        }
        if (rpcConfig.backup_endpoints) {
          rpcConfig.backup_endpoints.forEach(url => rpcs.add(url))
        }
      }
    }

    console.log(`Loaded ${rpcs.size} RPC endpoints from ${yamlPath}`)
    return rpcs
  } catch (error) {
    console.warn(`Failed to load RPC config from ${yamlPath}:`, error)
    return new Set<string>()
  }
}

/**
 * Load providers from a list of RPC endpoints
 *
 * This class manages concurrent access to providers, racing them as soon as they are created for
 * the `getTxReceipt` method, or managing a singleton promise requested with `forChainId` method,
 * resolved to the first RPC responding for that chainId, even before the chainIds of all
 * providers are known.
 * It also ensures that all providers are destroyed when the instance is destroyed.
 *
 * Enhanced with YAML config support and automatic RPC failover.
 *
 * @param argv - Object with either `rpcs`, `rpcs-file`, or `yaml-config` property
 * @returns instance of Providers manager class
 * @throws Error if no providers are found
 **/
export class Providers {
  #endpoints: Promise<Set<string>>
  #providersList?: Promise<(readonly [provider: JsonRpcApiProvider, endpoint: string])[]>
  #providersPromises: Record<number, Promise<JsonRpcApiProvider>> = {}
  #promisesCallbacks: Record<
    number,
    readonly [resolve: (provider: JsonRpcApiProvider) => void, reject: (reason: unknown) => void]
  > = {}
  #destroy!: (v: unknown) => void
  destroyed: Promise<unknown> = new Promise((resolve) => {
    this.#destroy = resolve
  })
  #complete!: (v: true) => void
  completed: true | Promise<true> = new Promise((resolve) => {
    this.#complete = resolve
  })
  // 添加RPC轮换相关属性
  #rpcFailureCount: Record<string, number> = {}
  #rpcLastFailure: Record<string, number> = {}
  #maxRetries = 3
  #retryDelay = 5000 // 5秒

  constructor(argv: { rpcs: string[] } | { 'rpcs-file': string } | { 'yaml-config': string }) {
    if ('rpcs' in argv) {
      this.#endpoints = Promise.resolve(
        new Set([
          ...argv.rpcs,
          ...Object.entries(process.env)
            .filter(([env, val]) => env.startsWith('RPC_') && val && RPCS_RE.test(val))
            .map(([, val]) => val!),
        ]),
      )
    } else if ('yaml-config' in argv) {
      // 支持YAML配置文件
      this.#endpoints = this.#loadFromYamlConfig(argv['yaml-config'])
    } else {
      this.#endpoints = readFile(argv['rpcs-file'], 'utf8')
        .catch(() => '')
        .then((file) => {
          const rpcs = new Set<string>()
          for (const line of file.toString().split(/(?:\r\n|\r|\n)/g)) {
            const match = line.match(RPCS_RE)
            if (match) rpcs.add(match[0])
          }
          for (const [env, val] of Object.entries(process.env)) {
            if (env.startsWith('RPC_') && val && RPCS_RE.test(val)) rpcs.add(val)
          }
          return rpcs
        })
    }
  }

  /**
   * 从YAML配置文件加载RPC端点
   */
  async #loadFromYamlConfig(yamlPath: string): Promise<Set<string>> {
    const yamlRpcs = await loadRpcFromYaml(yamlPath)

    // 同时加载环境变量中的RPC
    const envRpcs = new Set<string>()
    for (const [env, val] of Object.entries(process.env)) {
      if (env.startsWith('RPC_') && val && RPCS_RE.test(val)) {
        envRpcs.add(val)
      }
    }

    // 合并所有RPC端点
    const allRpcs = new Set([...yamlRpcs, ...envRpcs])
    console.log(`Total RPC endpoints loaded: ${allRpcs.size}`)
    return allRpcs
  }

  destroy() {
    this.#destroy(null)
  }

  /**
   * 检查RPC是否应该被跳过（基于失败次数和时间）
   */
  #shouldSkipRpc(endpoint: string): boolean {
    const failureCount = this.#rpcFailureCount[endpoint] || 0
    const lastFailure = this.#rpcLastFailure[endpoint] || 0
    const now = Date.now()

    if (failureCount >= this.#maxRetries) {
      // 如果超过最大重试次数，检查是否已经过了重试延迟时间
      if (now - lastFailure < this.#retryDelay) {
        return true // 跳过这个RPC
      } else {
        // 重置失败计数，给RPC一个新的机会
        this.#rpcFailureCount[endpoint] = 0
        delete this.#rpcLastFailure[endpoint]
      }
    }

    return false
  }

  /**
   * 记录RPC失败
   */
  #recordRpcFailure(endpoint: string): void {
    this.#rpcFailureCount[endpoint] = (this.#rpcFailureCount[endpoint] || 0) + 1
    this.#rpcLastFailure[endpoint] = Date.now()
    console.warn(`RPC ${endpoint} failed (${this.#rpcFailureCount[endpoint]}/${this.#maxRetries})`)
  }

  /**
   * 记录RPC成功
   */
  #recordRpcSuccess(endpoint: string): void {
    if (this.#rpcFailureCount[endpoint]) {
      console.log(`RPC ${endpoint} recovered`)
      delete this.#rpcFailureCount[endpoint]
      delete this.#rpcLastFailure[endpoint]
    }
  }

  /**
   * Trigger fetching providers from RPC endpoints, with their networks in parallel
   * Enhanced with RPC failure tracking and automatic failover
   **/
  #loadProviders(): Promise<(readonly [provider: JsonRpcApiProvider, endpoint: string])[]> {
    if (this.#providersList) return this.#providersList

    const readyPromises: Promise<unknown>[] = []
    return (this.#providersList = this.#endpoints
      .then((rpcs) =>
        [...rpcs]
          .filter(endpoint => !this.#shouldSkipRpc(endpoint)) // 过滤掉失败的RPC
          .map((endpoint) => {
            let provider: JsonRpcApiProvider
            let providerReady: Promise<JsonRpcApiProvider>

            if (endpoint.startsWith('ws')) {
              const provider_ = new WebSocketProvider(endpoint)
              providerReady = new Promise((resolve, reject) => {
                provider_.websocket.onerror = (error) => {
                  this.#recordRpcFailure(endpoint)
                  reject(error)
                }
                provider_
                  ._waitUntilReady()
                  .then(() => {
                    this.#recordRpcSuccess(endpoint)
                    resolve(provider_)
                  })
                  .catch((error) => {
                    this.#recordRpcFailure(endpoint)
                    reject(error)
                  })
              })
              provider = provider_
            } else if (endpoint.startsWith('http')) {
              provider = new JsonRpcProvider(endpoint)
              providerReady = Promise.resolve(provider)
            } else {
              throw new Error(
                `Unknown JSON RPC protocol in endpoint (should be wss?:// or https?://): ${endpoint}`,
              )
            }

            void this.destroyed.then(() => provider.destroy()) // schedule cleanup
            readyPromises.push(
              // wait for connection and check network in background
              withTimeout(
                providerReady.then((provider) => getProviderNetwork(provider)),
                30e3,
                `Timeout connecting to ${endpoint}`,
                this.destroyed,
              )
                .then(({ chainId }) => {
                  this.#recordRpcSuccess(endpoint)
                  if (chainId in this.#promisesCallbacks) {
                    const [resolve] = this.#promisesCallbacks[+chainId]
                    delete this.#promisesCallbacks[+chainId]
                    resolve(provider)
                  } else if (!(chainId in this.#providersPromises)) {
                    this.#providersPromises[+chainId] = Promise.resolve(provider)
                  } else {
                    throw new Error(`Raced by a faster provider`)
                  }
                })
                .catch((reason) => {
                  // 记录失败并销毁provider
                  this.#recordRpcFailure(endpoint)
                  provider.destroy()
                }),
            )
            return [provider, endpoint] as const
          }),
      )
      .finally(() => {
        void Promise.allSettled(readyPromises).then(() => {
          for (const [chainId, [, reject]] of Object.entries(this.#promisesCallbacks)) {
            // if `forChainId` in the meantime requested a provider that was not found, reject it
            reject(
              new Error(
                `Could not find provider for chain "${chainNameFromId(+chainId)}" [${chainId}]`,
              ),
            )
            delete this.#promisesCallbacks[+chainId]
          }
          this.#complete(true)
          this.completed = true
        })
      }))
  }

  /**
   * Ask for a provider for a given chainId, or wait for it to be available
   * @param chainId - chainId to get a provider for
   * @returns Promise for a provider for the given chainId
   **/
  async forChainId(chainId: number | string): Promise<JsonRpcApiProvider> {
    if (typeof chainId === 'string') throw new Error('non-EVM chains are not supported yet')

    if (chainId in this.#providersPromises) return this.#providersPromises[chainId]
    if (this.completed === true)
      throw new Error(
        `Could not find provider for chain "${chainNameFromId(chainId)}" [${chainId}]`,
      )
    this.#providersPromises[chainId] = new Promise((resolve, reject) => {
      this.#promisesCallbacks[chainId] = [resolve, reject]
    })
    void this.#loadProviders()
    return this.#providersPromises[chainId]
  }

  /**
   * Get transaction receipt from any of the providers;
   * Races them even before network is known, returning first to respond.
   * Continues populating the list of providers in the background after resolved.
   * @param txHash - transaction hash to get receipt for
   * @returns Promise for the transaction receipt, with provider in TransactionReceipt.provider
   **/
  async getTxReceipt(txHash: string): Promise<TransactionReceipt> {
    return this.#loadProviders().then((providers) =>
      Promise.any(
        providers.map(([provider, endpoint]) =>
          withTimeout(
            provider.getTransactionReceipt(txHash),
            30e3,
            `Timeout fetching tx=${txHash} from "${endpoint}"`,
            this.destroyed,
          ).then((receipt) => {
            if (!receipt) throw new Error(`Transaction=${txHash} not found in "${endpoint}"`)
            return receipt
          }),
        ),
      ),
    )
  }
}
