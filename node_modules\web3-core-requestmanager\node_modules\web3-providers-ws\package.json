{"name": "web3-providers-ws", "version": "1.10.4", "description": "Module to handle web3 RPC connections over WebSockets.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-providers-ws", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib types"}, "types": "types/index.d.ts", "main": "lib/index.js", "dependencies": {"eventemitter3": "4.0.4", "web3-core-helpers": "1.10.4", "websocket": "^1.0.32"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.9.5"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}