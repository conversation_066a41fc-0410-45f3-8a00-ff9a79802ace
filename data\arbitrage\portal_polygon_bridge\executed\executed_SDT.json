[{"opportunity_hash": "SDT_300.0_2025-06-05 07:40:03", "symbol": "SDT", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "", "time": "2025-06-05 07:40:05", "usdt_input": 300.0, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 300.0 USDT，可用: 156.395561 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_SDT.log"}, {"opportunity_hash": "SDT_300.0_2025-06-05 08:13:24", "symbol": "SDT", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "", "time": "2025-06-05 08:13:26", "usdt_input": 300.0, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 300.0 USDT，可用: 140.395561 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_SDT.log"}, {"opportunity_hash": "SDT_233.32_2025-06-05 08:47:25", "symbol": "SDT", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "", "time": "2025-06-05 08:47:27", "usdt_input": 233.32, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 233.32 USDT，可用: 140.395561 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_SDT.log"}]