{"name": "@ethereumjs/common", "version": "2.6.5", "description": "Resources common to all Ethereum implementations", "license": "MIT", "keywords": ["ethereum", "ethereumjs", "constants", "parameters", "genesis", "networks", "bootstrap"], "files": ["dist", "dist.browser", "src"], "main": "dist/index.js", "types": "dist/index.d.ts", "browser": "dist.browser/index.js", "scripts": {"build": "npm run build:node && npm run build:browser", "build:node": "../../config/cli/ts-build.sh node", "build:browser": "../../config/cli/ts-build.sh browser", "prepublishOnly": "../../config/cli/prepublish.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "tsc": "../../config/cli/ts-compile.sh", "lint": "../../config/cli/lint.sh", "lint:fix": "../../config/cli/lint-fix.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser", "test:node": "npm run tape -- ./tests/*.spec.ts", "test:browser": "karma start karma.conf.js", "docs:build": "typedoc --options typedoc.js"}, "dependencies": {"crc-32": "^1.2.0", "ethereumjs-util": "^7.1.5"}, "devDependencies": {"@types/node": "^16.11.7", "@types/tape": "^4.13.2", "eslint": "^6.8.0", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-tap": "^4.2.0", "karma-typescript": "^5.5.3", "nyc": "^15.1.0", "prettier": "^2.0.5", "tape": "^5.3.1", "typedoc": "^0.22.4", "ts-node": "^10.2.1", "typescript": "^4.4.2"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/common#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+common%22"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com"}]}