[{"opportunity_hash": "PGEN_40.02_2025-06-07 15:54:19", "symbol": "PGEN", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "", "time": "2025-06-07 15:57:45", "usdt_input": 40.02, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: None", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_PGEN.log"}]