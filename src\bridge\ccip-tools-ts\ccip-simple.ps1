# 简化的CCIP PowerShell脚本
# 解决Windows PowerShell执行问题

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

# 设置默认配置文件路径
$DefaultConfig = "..\..\..\config\rpc.yaml"
$CCIPScript = ".\src\index.ts"

# 检查配置文件
if (-not (Test-Path $DefaultConfig)) {
    Write-Host "❌ Configuration file not found: $DefaultConfig" -ForegroundColor Red
    Write-Host "Please ensure config\rpc.yaml exists in your project root." -ForegroundColor Yellow
    exit 1
}

# 检查脚本文件
if (-not (Test-Path $CCIPScript)) {
    Write-Host "❌ CCIP script not found: $CCIPScript" -ForegroundColor Red
    Write-Host "Please ensure you're running this from the ccip-tools-ts directory." -ForegroundColor Yellow
    exit 1
}

# 构建完整的参数列表
$AllArgs = @()
if ($Arguments) {
    $AllArgs += $Arguments
}

# 检查是否已经包含yaml-config参数
$hasYamlConfig = $false
foreach ($arg in $AllArgs) {
    if ($arg -like "*yaml-config*") {
        $hasYamlConfig = $true
        break
    }
}

# 如果没有yaml-config参数，添加默认配置
if (-not $hasYamlConfig) {
    $AllArgs += "--yaml-config"
    $AllArgs += $DefaultConfig
}

# 显示执行的命令
Write-Host "🚀 Executing: npx tsx $CCIPScript $($AllArgs -join ' ')" -ForegroundColor Cyan

# 执行命令
try {
    & npx tsx $CCIPScript @AllArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Command completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Command failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "❌ Error executing command: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
