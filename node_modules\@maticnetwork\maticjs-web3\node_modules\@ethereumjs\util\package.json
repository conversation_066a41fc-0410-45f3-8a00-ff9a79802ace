{"name": "@ethereumjs/util", "version": "8.1.0", "description": "A collection of utility functions for Ethereum", "keywords": ["ethereum", "utilities", "utils"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/util#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+util%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "EthereumJS Team", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter"}, {"name": "<PERSON>", "url": "https://github.com/SilentCicero"}, {"name": "Mr. <PERSON>", "url": "https://github.com/MrChico"}, {"name": "Dũng Trần", "email": "<EMAIL>", "url": "https://github.com/tad88dev"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic"}, {"name": "<PERSON>", "url": "https://github.com/tgerring"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/asinyagin"}], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "docs:build": "npx typedoc --options typedoc.js", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start karma.conf.js", "test:node": "npm run tape -- test/*.spec.ts", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@ethereumjs/rlp": "^4.0.1", "ethereum-cryptography": "^2.0.0", "micro-ftch": "^0.3.1"}, "devDependencies": {"@types/bn.js": "^5.1.0", "@types/secp256k1": "^4.0.1"}, "engines": {"node": ">=14"}}