import type { CryptoCurrency, TokenCurrency } from "@ledgerhq/types-cryptoassets";
import { TonJettonToken } from "./data/ton-jetton";
import { ERC20Token } from "./types";
type TokensListOptions = {
    withDelisted: boolean;
};
export declare function createTokenHash(token: TokenCurrency): string;
/**
 * Only for jest purpose, clear all the init list
 */
export declare function __clearAllLists(): void;
/**
 *
 */
export declare function listTokens(options?: Partial<TokensListOptions>): TokenCurrency[];
/**
 *
 */
export declare function listTokensForCryptoCurrency(currency: CryptoCurrency, options?: Partial<TokensListOptions>): TokenCurrency[];
/**
 *
 */
export declare function listTokenTypesForCryptoCurrency(currency: CryptoCurrency): string[];
/**
 *
 */
export declare function findTokenByTicker(ticker: string): TokenCurrency | undefined;
/**
 *
 */
export declare function findTokenById(id: string): TokenCurrency | undefined;
export declare function findTokenByAddress(address: string): TokenCurrency | undefined;
export declare function findTokenByAddressInCurrency(address: string, currencyId: string): TokenCurrency | undefined;
/**
 *
 */
export declare const hasTokenId: (id: string) => boolean;
/**
 *
 */
export declare function getTokenById(id: string): TokenCurrency;
export declare function addTokens(list: (TokenCurrency | undefined)[]): void;
export declare function convertERC20([parentCurrencyId, token, ticker, magnitude, name, ledgerSignature, contractAddress, disableCountervalue, delisted,]: ERC20Token): TokenCurrency | undefined;
export declare function convertJettonToken([address, name, ticker, magnitude, delisted]: TonJettonToken): TokenCurrency | undefined;
export {};
//# sourceMappingURL=tokens.d.ts.map