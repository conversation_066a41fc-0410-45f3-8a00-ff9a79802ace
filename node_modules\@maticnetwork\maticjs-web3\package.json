{"name": "@maticnetwork/maticjs-web3", "version": "1.0.5", "description": "web3.js plugin for matic.js", "main": "dist/npm.export.js", "types": "dist/ts/index.d.ts", "scripts": {"build": "npm run build:dev", "build:link": "npm run build && npm link", "build:webpack": "npm run lint && webpack --config webpack/webpack.node.config.js", "build:pack": "npm run build:dev && npm pack", "build:dev": "cross-env NODE_ENV=development npm run build:webpack", "build:prod": "cross-env NODE_ENV=production npm run build:webpack", "deploy": "npm run build:dev && npm run build:prod", "prepublishOnly": "npm run deploy", "lint": "tslint src/**/*.ts", "lint:fix": "tslint src/**/*.ts --fix", "test": "npm run build:pack && cd test && npm run install:lib:test", "debug": "npm run build:pack && cd test && npm run install:lib:debug"}, "repository": {"type": "git", "url": "git+https://github.com/maticnetwork/maticjs-web3.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/maticnetwork/maticjs-web3/issues"}, "homepage": "https://github.com/maticnetwork/maticjs-web3#readme", "dependencies": {"web3": "^1.8.0"}, "peerDependencies": {"@maticnetwork/maticjs": "^3.2.0"}, "devDependencies": {"@maticnetwork/maticjs": "^3.5.0", "@types/web3": "1.0.19", "copy-webpack-plugin": "^6.1.1", "cross-env": "^7.0.3", "smart-banner-webpack-plugin": "^3.0.1", "ts-loader": "^7.0.1", "tslint": "^6.1.3", "typescript": "^4.4.2", "webpack": "^4.43.0", "webpack-cli": "^4.2.0", "webpack-merge": "^4.2.2"}}