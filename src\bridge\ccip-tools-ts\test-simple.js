// 简单的网络名称测试
const { resolveNetworkName, validateNetwork } = require('./src/config/rpc-integration.ts');

console.log('🧪 Testing Network Name Resolution');
console.log('==================================');

// 测试基本功能
const testCases = [
  'ethereum',
  'eth', 
  'polygon',
  'matic',
  'bsc',
  'sepolia',
  'fuji',
  '1',
  '137'
];

for (const testCase of testCases) {
  try {
    const result = resolveNetworkName(testCase);
    console.log(`✅ "${testCase}" -> ${result}`);
  } catch (error) {
    console.log(`❌ "${testCase}" -> Error: ${error.message}`);
  }
}

console.log('\n✨ Basic test completed!');
