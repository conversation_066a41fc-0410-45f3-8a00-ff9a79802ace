/**
 * Returns the genesis state by network ID
 * @param id ID of the network (e.g. 1)
 * @returns Dictionary with genesis accounts
 *
 * @deprecated use {@link Common.genesisState} instead
 */
export declare function genesisStateById(id: number): any;
/**
 * Returns the genesis state by network name
 * @param name Name of the network (e.g. 'mainnet')
 * @returns Dictionary with genesis accounts
 *
 * @deprecated use {@link Common.genesisState} instead
 */
export declare function genesisStateByName(name: string): any;
