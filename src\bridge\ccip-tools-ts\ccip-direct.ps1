# Direct CCIP PowerShell Script
# Simple wrapper for CCIP tools

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

$DefaultConfig = "..\..\..\config\rpc.yaml"
$CCIPScript = ".\src\index.ts"

# Check if config file exists
if (-not (Test-Path $DefaultConfig)) {
    Write-Host "Error: Configuration file not found: $DefaultConfig" -ForegroundColor Red
    Write-Host "Please ensure config\rpc.yaml exists in your project root." -ForegroundColor Yellow
    exit 1
}

# Check if script file exists
if (-not (Test-Path $CCIPScript)) {
    Write-Host "Error: CCIP script not found: $CCIPScript" -ForegroundColor Red
    Write-Host "Please ensure you're running this from the ccip-tools-ts directory." -ForegroundColor Yellow
    exit 1
}

# If no arguments, show help
if (-not $Arguments) {
    Write-Host "CCIP Tools - PowerShell Helper" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\ccip-direct.ps1 <command> [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Green
    Write-Host "  list-networks"
    Write-Host "  getSupportedTokens <network>"
    Write-Host "  send <source> <router> <dest> --receiver <address>"
    Write-Host "  show <tx_hash>"
    Write-Host "  manualExec <tx_hash>"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Green
    Write-Host "  .\ccip-direct.ps1 list-networks"
    Write-Host "  .\ccip-direct.ps1 getSupportedTokens ethereum"
    Write-Host "  .\ccip-direct.ps1 send ethereum 0xRouter polygon --receiver 0xAddress"
    Write-Host ""
    exit 0
}

# Build argument list
$AllArgs = @()
$AllArgs += $Arguments

# Check if yaml-config is already included
$hasYamlConfig = $false
foreach ($arg in $AllArgs) {
    if ($arg -like "*yaml-config*") {
        $hasYamlConfig = $true
        break
    }
}

# Add default config if not present
if (-not $hasYamlConfig) {
    $AllArgs += "--yaml-config"
    $AllArgs += $DefaultConfig
}

# Execute command
Write-Host "Executing: npx tsx $CCIPScript $($AllArgs -join ' ')" -ForegroundColor Cyan

try {
    & npx tsx $CCIPScript @AllArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Command completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Command failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Error executing command: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
