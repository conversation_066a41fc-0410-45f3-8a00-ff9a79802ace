# KyberSwapClient 代理配置修改总结

## 修改目标
修改KyberSwapClient，使其在真实swap交易的完整过程（包括获取路由）时不使用代理IP，但在获取报价等API调用时仍然使用代理。同时解决真实交易时重复获取路由的问题，并增加滑点选项（1%和5%），确保真实交易时尝试完所有5个滑点。特别地，当输入代币是USDT时，不进行交易失败重试。

## 修改内容

### 1. 修改 `_make_request_with_retry` 方法
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第3077-3143行

**修改内容**:
- 添加了 `use_proxy: bool = True` 参数
- 根据 `use_proxy` 参数决定是否使用代理
- 当 `use_proxy=False` 时，明确不使用代理并输出日志

**修改前**:
```python
async def _make_request_with_retry(self, method: str, url: str, max_retries: int = 3, 
                                 retry_delay: int = 2, **kwargs) -> requests.Response:
    # 如果启用了代理，添加代理设置
    if self.proxy_enabled and self.proxies:
        kwargs['proxies'] = self.proxies
```

**修改后**:
```python
async def _make_request_with_retry(self, method: str, url: str, max_retries: int = 3, 
                                 retry_delay: int = 2, use_proxy: bool = True, **kwargs) -> requests.Response:
    # 根据use_proxy参数决定是否使用代理
    if use_proxy and self.proxy_enabled and self.proxies:
        kwargs['proxies'] = self.proxies
        print(f"🔌 使用代理发送请求: {self.proxies}")
    elif not use_proxy:
        print("🔄 不使用代理发送请求")
        kwargs.pop('proxies', None)
```

### 2. 修改 `_init_web3` 方法
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第91-142行

**修改内容**:
- 当有私钥时（真实交易模式），不使用代理连接RPC
- 添加了明确的日志输出

**修改前**:
```python
# 如果启用了代理，使用代理
if self.proxy_enabled and self.proxies:
    proxy = self.proxies.get('http')
    if proxy:
        print(f"🔌 使用SOCKS5代理连接RPC: {proxy}")
        self.web3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs={'proxies': self.proxies}))
```

**修改后**:
```python
# 真实交易时不使用代理，确保交易稳定性
print("🔄 真实交易模式：不使用代理连接RPC")
self.web3 = Web3(Web3.HTTPProvider(rpc_url))
```

### 3. 修改 `get_routes` 方法
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第251-254行, 第313-322行

**修改内容**:
- 添加了 `use_proxy: bool = True` 参数，默认使用代理（报价时），可设为False（真实交易时）
- 在调用 `_make_request_with_retry` 时传入 `use_proxy` 参数

**修改**:
```python
async def get_routes(self, token_in: str, token_out: str, amount_in: Union[str, float],
                    slippage: float = 0.5, is_native_in: bool = False,
                    save_gas: bool = False, excluded_sources: str = None,
                    is_wei_format: bool = True, use_proxy: bool = True) -> Dict[str, Any]:

# 调用时
response = await self._make_request_with_retry(
    method='get',
    url=api_url,
    params=params,
    headers=headers,
    timeout=15,
    use_proxy=use_proxy
)
```

### 4. 修改 `encode_route` 方法
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第1849-1851行, 第1907-1915行

**修改内容**:
- 添加了 `use_proxy: bool = False` 参数，默认不使用代理
- 在调用 `_make_request_with_retry` 时传入 `use_proxy` 参数

**修改**:
```python
async def encode_route(self, routes_data: Dict[str, Any], sender: str, recipient: str,
                    slippage_tolerance: float = 0.5, deadline: int = None,
                    source: str = "kyberswap-api-client", use_proxy: bool = False) -> Dict[str, Any]:

# 调用时
response = await self._make_request_with_retry(
    method='post',
    url=api_url,
    json=request_body,
    headers=headers,
    timeout=15,
    use_proxy=use_proxy
)
```

### 5. 解决重复路由获取问题
**文件**: `src/dex/KyberSwap/swap.py`
**位置**: 第1152-1186行

**问题**: 在真实交易时，代码会两次获取路由：
1. 在 `swap_tokens` 函数中预先获取路由（用于提取输出金额）
2. 在 `client.swap` 方法内部再次获取路由（用于实际交易）

**解决方案**:
- 移除 `swap_tokens` 函数中的预先路由获取
- 在交易完成后从 `client.swap` 返回结果中提取路由摘要信息
- 在 `KyberSwapClient` 的成功返回中添加 `route_summary` 字段

### 6. 增加滑点选项并优化重试逻辑
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第730-733行, 第1346-1385行

**修改内容**:
- 将滑点数组从3个增加到5个：`[0.1, 0.3, 0.6, 1.0, 2.0]`
- 将最大重试次数从3次增加到5次
- 优化错误处理逻辑，确保每次失败后都尝试下一个滑点

**修改前**:
```python
MAX_RETRIES = 3
retry_slippages = [0.1, 0.3, 0.6]  # 只有3个滑点
```

**修改后**:
```python
MAX_RETRIES = 5
retry_slippages = [0.1, 0.3, 0.6, 1.0, 2.0]  # 5个滑点，包括1%和2%
```

**滑点策略**:
- **0.1%**: 最低滑点，适合稳定币交易
- **0.3%**: 低滑点，适合主流代币
- **0.6%**: 中等滑点，适合一般代币
- **1.0%**: 较高滑点，适合波动性代币
- **2.0%**: 最高滑点，适合高波动性或流动性差的代币

### 7. 添加USDT输入时智能重试功能
**文件**: `src/dex/KyberSwap/client.py`
**位置**: 第744-753行, 第759行, 第1241行, 第1364-1394行

**修改内容**:
- 添加USDT地址识别逻辑，支持Ethereum和Polygon的USDT
- 添加交易构建成功标记 `transaction_built_successfully`
- USDT输入时：路由获取可以重试，但交易执行失败后不重试

**USDT地址列表**:
```python
usdt_addresses = [
    "******************************************".lower(),  # Ethereum USDT
    "******************************************".lower()   # Polygon USDT
]
```

**智能重试逻辑**:
1. **路由获取阶段**: USDT输入时可以正常重试5个滑点
2. **交易构建阶段**: 成功构建后设置 `transaction_built_successfully = True`
3. **交易执行阶段**: 如果是USDT且交易已构建成功，失败后不重试
4. **非USDT代币**: 保持原有的完整重试机制

**优势**:
- **平衡成功率和效率**: 路由获取可以重试确保找到最佳路由
- **避免无效重试**: 交易执行失败后不重试，节省时间和gas
- **智能判断**: 只在交易已成功构建后才跳过重试
- **保持兼容**: 其他代币保持原有的重试机制

### 8. 修改各API调用的代理使用策略

**真实交易中的路由获取** (`_execute_swap`):
```python
routes_data = await self.get_routes(
    token_in=token_in,
    token_out=token_out,
    amount_in=amount_in,
    slippage=slippage if simulate else 0.1,
    is_native_in=is_native_in,
    save_gas=save_gas,
    excluded_sources=excluded_sources,
    use_proxy=simulate  # 模拟交易时使用代理，真实交易时不使用代理
)
```

**报价查询中的路由获取** (`quote_command`):
```python
routes_data = loop.run_until_complete(
    client.get_routes(
        token_in=token_in_address,
        token_out=token_out_address,
        amount_in=amount_wei,
        slippage=slippage,
        is_native_in=is_native_in,
        save_gas=save_gas,
        excluded_sources=final_excluded_sources,
        use_proxy=True  # 获取报价时使用代理
    )
)
```

**构建交易数据** (`_build_swap_tx_params`):
```python
encoded_data = await self.encode_route(
    routes_data=routes_data,
    sender=self.address,
    recipient=receiver_address,
    slippage_tolerance=slippage,
    deadline=deadline,
    source="kyberswap-api-client",
    use_proxy=False  # 真实交易时不使用代理
)
```

**获取价格信息** (`get_eth_price_from_geckoterminal`):
```python
response = await self._make_request_with_retry(
    method='get',
    url=url,
    headers=headers,
    timeout=10,
    use_proxy=True  # 获取价格信息可以使用代理
)
```

## 代理使用策略总结

| 操作类型 | 是否使用代理 | 原因 |
|---------|-------------|------|
| 获取报价路由 (`get_routes` with `use_proxy=True`) | ✅ 使用 | API调用，可以使用代理避免速率限制 |
| 真实交易路由 (`get_routes` with `use_proxy=False`) | ❌ 不使用 | 真实交易过程，确保稳定性 |
| 编码交易数据 (`encode_route`) | ❌ 不使用 | 为真实交易准备数据，确保稳定性 |
| 获取价格信息 (`get_eth_price`) | ✅ 使用 | API调用，可以使用代理 |
| RPC连接（有私钥时） | ❌ 不使用 | 真实交易模式，确保交易稳定性 |
| RPC连接（无私钥时） | ✅ 使用 | 只读操作，可以使用代理 |

## 测试验证

创建了多个测试脚本来验证修改是否正确工作：

### 代理测试 (`test_proxy_modification.py`)
1. 测试获取报价路由（应该使用代理）
2. 测试真实交易路由（应该不使用代理）
3. 测试编码路由（应该不使用代理）
4. 测试获取ETH价格（应该使用代理）

### 滑点测试 (`test_slippage_modification.py`)
1. 验证滑点数组配置正确性
2. 测试每个滑点值的路由获取
3. 验证滑点重试逻辑

### USDT不重试测试 (`test_usdt_no_retry.py`)
1. 验证USDT地址识别正确性
2. 测试USDT输入时的单次交易模式
3. 对比USDT和非USDT代币的重试行为

## 使用方法

修改后的代码会自动根据操作类型决定是否使用代理：

- **获取报价时**: 自动使用代理（如果配置了代理）
- **真实交易时**: 整个交易过程（包括获取路由、编码交易数据）都不使用代理，确保交易稳定性
- **价格查询时**: 自动使用代理（如果配置了代理）

### 关键区别：

1. **报价查询** (`python -m src.dex.KyberSwap.swap quote`): 使用代理
2. **真实交易** (`python -m src.dex.KyberSwap.swap swap --real`): 完整过程不使用代理，尝试5个滑点
3. **模拟交易** (`python -m src.dex.KyberSwap.swap swap --simulate`): 使用代理

### 真实交易滑点重试流程：

#### 普通代币（非USDT）：
1. **第1次尝试**: 0.1% 滑点（最保守）
2. **第2次尝试**: 0.3% 滑点（如果第1次失败）
3. **第3次尝试**: 0.6% 滑点（如果第2次失败）
4. **第4次尝试**: 1.0% 滑点（如果第3次失败）
5. **第5次尝试**: 2.0% 滑点（如果第4次失败）

#### USDT输入：
1. **路由获取**: 可重试5个滑点（0.1% → 0.3% → 0.6% → 1.0% → 2.0%）
2. **交易执行**: 构建成功后，失败不重试

每次重试都会：
- 增加滑点容忍度
- 启用gas优化
- 增加gas乘数（1.5倍）

### 特殊处理：
- **USDT路由获取**: 正常重试，确保找到最佳路由
- **USDT交易执行**: 构建成功后失败不重试，提高效率
- **其他代币**: 保持完整的多滑点重试机制

用户无需手动配置，系统会智能选择最适合的网络策略和滑点设置。
