{"version": 3, "file": "eip2930Transaction.js", "sourceRoot": "", "sources": ["../src/eip2930Transaction.ts"], "names": [], "mappings": ";;AAAA,qDAUwB;AAExB,uDAAmD;AACnD,mCAQgB;AAEhB,iCAA0D;AAE1D,MAAM,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAM,uBAAuB,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;AAElG;;;;;GAKG;AACH,MAAqB,4BAA6B,SAAQ,iCAA6C;IAmIrG;;;;;;OAMG;IACH,YAAmB,MAA+B,EAAE,OAAkB,EAAE;;QACtE,KAAK,iCAAM,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC,CAAA;QAnIpD;;;;;WAKG;QACO,qBAAgB,GAAG,QAAQ,CAAA;QA8HnC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;QAEhD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAA;QAEtC,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAEtE,kCAAkC;QAClC,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,kBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEnE,IAAI,CAAC,+BAA+B,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,6BAAW,CAAC,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAA;YAC3E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAA;YACrF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,IAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAA,2BAAoB,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACpD;QACD,MAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IA3KD;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,CAAC,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,CAAC,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,CAAC,CAAA;IACf,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAA+B,EAAE,OAAkB,EAAE;QAC5E,OAAO,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACrE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,sFAAsF,gBAAgB,eAAe,UAAU;iBAC5H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,QAAQ,CAAC,KAAK,CAAC,EAAE,CACrB,CAAA;SACF;QAED,MAAM,MAAM,GAAG,qBAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,4BAA4B,CAAC,eAAe,CAAC,MAAa,EAAE,IAAI,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACxE,OAAO,4BAA4B,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IACxE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAoC,EAAE,OAAkB,EAAE;QACtF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEzF,IAAA,yCAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,MAAM,eAAe,GAAe,EAAE,CAAA;QAEtC,OAAO,IAAI,4BAA4B,CACrC;YACE,OAAO,EAAE,IAAI,oBAAE,CAAC,OAAO,CAAC;YACxB,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,eAAe;YACzC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,oBAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAC1C,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA4DD;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,CAAA;QAC/B,IAAI,CAAC,KAAK,CAAC,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAEvE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,oCAAkB,EAAC,IAAI,CAAC,OAAO,CAAC;YAChC,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACpE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,qBAAG,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,qBAAG,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,CAAA;QACjF,IAAI,WAAW,EAAE;YACf,OAAO,IAAA,2BAAS,EAAC,OAAO,CAAC,CAAA;SAC1B;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;aAC9C;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACI,kBAAkB;;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAElD,uGAAuG;QACvG,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,IAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QAC9B,IAAI;YACF,OAAO,IAAA,2BAAS,EACd,OAAO,EACP,OAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACnE,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAA,oCAAkB,EAAC,CAAE,CAAC,CACvB,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/C,MAAM,IAAI,mCAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAA;QAEvD,OAAO,4BAA4B,CAAC,UAAU,CAC5C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,GAAG,EAAE,CAAC;YACjB,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;SACb,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAErE,OAAO;YACL,OAAO,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,OAAO,CAAC;YAC9B,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACtD,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,mFAAmF;QACnF,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,oBAAoB,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,mCAAI,CAAC,EAAE,CAAA;QACxF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;CACF;AAxZD,+CAwZC"}