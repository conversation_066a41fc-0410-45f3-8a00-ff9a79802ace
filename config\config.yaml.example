# CEX-DEX 套利系统配置示例
# 请复制此文件为 config.yaml 并填写您的实际信息

# 中心化交易所配置
cex:
  gate:
    api_key: "您的Gate.io API密钥"  # 替换为您的实际API密钥
    secret_key: "您的Gate.io密钥"   # 替换为您的实际密钥
    password: ""                    # 如果有密码，请填写
    enable_rate_limit: true         # 启用API访问频率限制
    timeout: 30000                  # 请求超时时间(毫秒)

# 去中心化交易所配置
dex:
  astar:
    # 钱包配置
    wallet:
      private_key: "您的钱包私钥"   # 替换为您的实际私钥
    
    # RPC配置
    rpc:
      endpoint: "https://astar.public.blastapi.io" # 默认RPC端点
      backup_endpoints:
        - "https://astar-rpc.dwellir.com"
        - "https://astar.api.onfinality.io/public"
    
    # 代理配置(可选)
    proxy:
      enabled: false
      host: "127.0.0.1"
      port: 7890
      auth:
        username: ""
        password: ""

# 套利配置
arbitrage:
  # 套利阈值设置
  threshold:
    min_profit_percentage: 0.5  # 最小利润百分比
  
  # 风险控制
  risk_control:
    max_order_amount: 100       # 单笔订单最大金额(USD)
    max_daily_amount: 1000      # 每日最大交易金额(USD)
  
  # 交易设置
  trading:
    slippage_tolerance: 0.5     # DEX滑点容忍度(%)
    gas_price_multiplier: 1.1   # Gas价格倍数

# 日志设置
logging:
  level: "INFO"                 # 日志级别: DEBUG, INFO, WARNING, ERROR
  file: "logs/arbitrage.log"    # 日志文件路径
  rotation: true                # 是否启用日志轮转
  max_size: 10                  # 单个日志文件最大大小(MB)
  backup_count: 5               # 保留的日志文件数量 