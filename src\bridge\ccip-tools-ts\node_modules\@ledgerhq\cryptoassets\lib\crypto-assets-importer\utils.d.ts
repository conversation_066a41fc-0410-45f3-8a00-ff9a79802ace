/// <reference types="node" />
/// <reference types="node" />
import { CALServiceOutput } from "./fetch";
import { ERC20Token } from "../types";
export declare const getErc20DescriptorsAndSignatures: (tokens: Pick<CALServiceOutput, "id" | "blockchain_name" | "contract_address" | "decimals" | "delisted" | "name" | "ticker" | "live_signature">[], chainId: number) => {
    erc20: ERC20Token[];
    erc20Signatures: Buffer;
};
//# sourceMappingURL=utils.d.ts.map