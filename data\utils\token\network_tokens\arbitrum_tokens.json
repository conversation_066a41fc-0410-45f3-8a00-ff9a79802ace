[{"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "ATH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Tether USD", "symbol": "USDT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Animecoin", "symbol": "ANIME", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ether.fi governance token", "symbol": "ETHFI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Ninja Squad Token", "symbol": "NST", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GMX", "symbol": "GMX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Xai", "symbol": "XAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "KNS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USD Coin", "symbol": "USDC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "YBR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "PENDLE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Sepolia ETH", "symbol": "SETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "CATCH", "symbol": "CATCH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Arbitrum", "symbol": "ARB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Autonomi", "symbol": "ANT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LayerZero", "symbol": "ZRO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MAGIC", "symbol": "MAGIC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "XBorg", "symbol": "XBG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Livepeer Token", "symbol": "LPT", "decimals": null, "network": "arbitrum"}, {"address": "0xaaa6c1e32c55a7bfa8066a6fae9b42650f262418", "name": "<PERSON><PERSON>", "symbol": "RAM", "decimals": null, "network": "arbitrum"}, {"address": "0xda10009cbd5d07dd0cecc66161fc93d7c9000da1", "name": "Dai Stablecoin", "symbol": "DAI", "decimals": null, "network": "arbitrum"}, {"address": "0xd56734d7f9979dd94fae3d67c7e928234e71cd4c", "name": "TIA", "symbol": "TIA.n", "decimals": null, "network": "arbitrum"}, {"address": "0xd77b108d4f6cefaa0cae9506a934e825becca46e", "name": "WINR", "symbol": "WINR", "decimals": null, "network": "arbitrum"}, {"address": "0xff970a61a04b1ca14834a43f5de4533ebddb5cc8", "name": "USD Coin (Arb1)", "symbol": "USDC", "decimals": null, "network": "arbitrum"}, {"address": "0xe80772eaf6e2e18b651f160bc9158b2a5cafca65", "name": "USD+", "symbol": "USD+", "decimals": null, "network": "arbitrum"}, {"address": "0x7be5dd337cc6ce3e474f64e2a92a566445290864", "name": "OpenLeverage Token V2", "symbol": "OLE", "decimals": null, "network": "arbitrum"}, {"address": "0x11cdb42b0eb46d95f990bedd4695a6e3fa034978", "name": "Curve DAO Token", "symbol": "CRV", "decimals": null, "network": "arbitrum"}, {"address": "0x18c11fd286c5ec11c3b683caa813b77f5163a122", "name": "Gains Network", "symbol": "GNS", "decimals": null, "network": "arbitrum"}, {"address": "0xdadeca1167fe47499e53eb50f261103630974905", "name": "Neuron", "symbol": "NRN", "decimals": null, "network": "arbitrum"}, {"address": "0x94fcd9c18f99538c0f7c61c5500ca79f0d5c4dab", "name": "KIMA", "symbol": "KIMA", "decimals": null, "network": "arbitrum"}, {"address": "0x7e7a7c916c19a45769f6bdaf91087f93c6c12f78", "name": "Eigenpie", "symbol": "EGP", "decimals": null, "network": "arbitrum"}, {"address": "0xba5ddd1f9d7f570dc94a51479a000e3bce967196", "name": "<PERSON><PERSON>", "symbol": "AAVE", "decimals": null, "network": "arbitrum"}, {"address": "0x83d6c8c06ac276465e4c92e7ac8c23740f435140", "name": "HMX", "symbol": "HMX", "decimals": null, "network": "arbitrum"}, {"address": "0xeb466342c4d449bc9f53a865d5cb90586f405215", "name": "Axelar Wrapped USDC", "symbol": "axlUSDC", "decimals": null, "network": "arbitrum"}, {"address": "0xf97f4df75117a78c1a5a0dbb814af92458539fb4", "name": "ChainLink Token", "symbol": "LINK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Lido DAO Token", "symbol": "LDO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Pear", "symbol": "PEAR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Axelar Wrapped LAVA", "symbol": "LAVA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Arbitrum tBTC v2", "symbol": "tBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON>", "symbol": "MAIA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "KIP Protocol", "symbol": "KIP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Account Abstraction Incentive", "symbol": "AA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Equilibria Token", "symbol": "EQB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "REDP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Sperax", "symbol": "SPA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Sperax USD", "symbol": "USDs", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "HERMES", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Uniswap", "symbol": "UNI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USDe", "symbol": "USDe", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "symbol": "wstETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ZTX", "symbol": "ZTX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Kuma World", "symbol": "KUMA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Rocket Pool ETH", "symbol": "rETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Spell Token", "symbol": "SPELL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Curve.Fi USD Stablecoin", "symbol": "crvUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MiL.k", "symbol": "MLK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "KNOW", "symbol": "KNOW", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "PancakeSwap Token", "symbol": "Cake", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ApeX Token", "symbol": "APEX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SushiToken", "symbol": "SUSHI", "decimals": null, "network": "arbitrum"}, {"address": "0x8b0e6f19ee57089f7649a455d89d7bc6314d04e8", "name": "DMT", "symbol": "DMT", "decimals": null, "network": "arbitrum"}, {"address": "0x17fc002b466eec40dae837fc4be5c67993ddbd6f", "name": "Frax", "symbol": "FRAX", "decimals": null, "network": "arbitrum"}, {"address": "0x3082cc23568ea640225c2467653db90e9250aaa0", "name": "<PERSON><PERSON><PERSON>", "symbol": "RDNT", "decimals": null, "network": "arbitrum"}, {"address": "0xf929de51d91c77e42f5090069e0ad7a09e513c73", "name": "FOX", "symbol": "FOX", "decimals": null, "network": "arbitrum"}, {"address": "0x4debfb9ed639144cf1e401674af361ffffcefb58", "name": "CADAICO", "symbol": "CADAI", "decimals": null, "network": "arbitrum"}, {"address": "0x59062301fb510f4ea2417b67404cb16d31e604ba", "name": "LogX Network", "symbol": "LOGX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Magic Internet Money", "symbol": "MIM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON>zo Restaked ETH", "symbol": "ezETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "HeyAnon", "symbol": "<PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EYWA", "symbol": "EYWA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GameSwift", "symbol": "GSWIFT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Coinbase Wrapped BTC", "symbol": "cbBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "UXLINK Token", "symbol": "UXLINK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Graph <PERSON>", "symbol": "GRT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GAM3S.GG", "symbol": "G3", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "G@ARB", "symbol": "@G", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped eETH", "symbol": "weETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Sugarverse", "symbol": "CNDY", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Universal ETH", "symbol": "uniETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "CARV", "symbol": "CARV", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Silo Governance Token", "symbol": "Silo", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "StargateToken", "symbol": "STG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Bonsai", "symbol": "Bonsai", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Orderly Network", "symbol": "ORDER", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Camelot token", "symbol": "GRAIL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LilAI", "symbol": "LILAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "OpulousToken", "symbol": "OPUL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "StarHeroes", "symbol": "STAR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "KelpDao Restaked ETH", "symbol": "rsETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Layer3", "symbol": "L3", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped SOL", "symbol": "SOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Helping Hearts International", "symbol": "HHI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "VEE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Empyreal", "symbol": "EMP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "THORWallet Governance Token", "symbol": "TGT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "AISweatShop", "symbol": "DeFAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "VCNT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Gyro Dollar", "symbol": "GYD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer", "symbol": "BAL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wootrade Network", "symbol": "WOO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "dForce USD", "symbol": "USX", "decimals": null, "network": "arbitrum"}, {"address": "0x99d85550623b249c54ee6e7f42861dcefa3ee8d7", "name": "Balancer USX/USDC StablePool", "symbol": "USX/USDC BPT", "decimals": null, "network": "arbitrum"}, {"address": "0x6dd963c510c2d2f09d5eddb48ede45fed063eb36", "name": "Factor", "symbol": "FCTR", "decimals": null, "network": "arbitrum"}, {"address": "0x95146881b86b3ee99e63705ec87afe29fcc044d9", "name": "Vertex", "symbol": "VRTX", "decimals": null, "network": "arbitrum"}, {"address": "0xfa296fca3c7dba4a92a42ec0b5e2138da3b29050", "name": "<PERSON><PERSON><PERSON>", "symbol": "shi<PERSON>i", "decimals": null, "network": "arbitrum"}, {"address": "0xca4e51f6ad4afd9d1068e5899de9dd7d73f3463d", "name": "AARK Token", "symbol": "AARK", "decimals": null, "network": "arbitrum"}, {"address": "0xc9d23ed2adb0f551369946bd377f8644ce1ca5c4", "name": "Hyperlane", "symbol": "HYPER", "decimals": null, "network": "arbitrum"}, {"address": "0x1cd9a56c8c2ea913c70319a44da75e99255aa46f", "name": "Orbiter Token", "symbol": "OBT", "decimals": null, "network": "arbitrum"}, {"address": "0xb6093b61544572ab42a0e43af08abafd41bf25a6", "name": "WeatherXM", "symbol": "WXM", "decimals": null, "network": "arbitrum"}, {"address": "0x5117f4ad0bc70dbb3b05bf39a1ec1ee40dd67654", "name": "Avive", "symbol": "Avive", "decimals": null, "network": "arbitrum"}, {"address": "0x4a24b101728e07a52053c13fb4db2bcf490cabc3", "name": "<PERSON><PERSON><PERSON>", "symbol": "AIUS", "decimals": null, "network": "arbitrum"}, {"address": "0x3f56e0c36d275367b8c502090edf38289b3dea0d", "name": "Mai Stablecoin", "symbol": "MAI", "decimals": null, "network": "arbitrum"}, {"address": "0x7f9fbf9bdd3f4105c478b996b648fe6e828a1e98", "name": "ApeCoin", "symbol": "APE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "m<PERSON><PERSON><PERSON>", "symbol": "m<PERSON><PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LqdrV2", "symbol": "Lqdr", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "VKA", "symbol": "VKA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "RDNT-WETH", "symbol": "RDNT-WETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Mag<PERSON> locked DLP", "symbol": "mDLP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Hydranet", "symbol": "HDN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Bitcoin", "symbol": "BTC.b", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "AxonDAO Governance Token", "symbol": "AXGT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Gho Token", "symbol": "GHO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Premia", "symbol": "PREMIA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Slingshot", "symbol": "SLING", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Overtime DAO Token", "symbol": "OVER", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fr<PERSON>", "symbol": "frxETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wall Street Games", "symbol": "WSG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wormhole Token", "symbol": "W", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fun USD", "symbol": "fUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EURC", "symbol": "EURC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Static Aave Arbitrum USDCn", "symbol": "stataArbUSDCn", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ArbiDex Token", "symbol": "ARX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Bonk", "symbol": "Bonk", "decimals": null, "network": "arbitrum"}, {"address": "0xe85b662fe97e8562f4099d8a1d5a92d4b453bf30", "name": "Thales DAO Token", "symbol": "THALES", "decimals": null, "network": "arbitrum"}, {"address": "0xaf7e3f16d747e77e927dc94287f86eb95a64d83d", "name": "VEMP", "symbol": "VEMP", "decimals": null, "network": "arbitrum"}, {"address": "0x7ae9ab13fc8945323b778b3f8678145e80ec2efb", "name": "WorldBrain Coin", "symbol": "WBC", "decimals": null, "network": "arbitrum"}, {"address": "0x968be3f7bfef0f8edc3c1ad90232ebb0da0867aa", "name": "Seedworld", "symbol": "SWORLD", "decimals": null, "network": "arbitrum"}, {"address": "0xa71e2738704e367798baa2755af5a10499634953", "name": "<PERSON><PERSON>", "symbol": "AVRK", "decimals": null, "network": "arbitrum"}, {"address": "0x7c8a1a80fdd00c9cccd6ebd573e9ecb49bfa2a59", "name": "AICODE", "symbol": "AICODE", "decimals": null, "network": "arbitrum"}, {"address": "0xcb8fa9a76b8e203d8c3797bf438d8fb81ea3326a", "name": "Alchemix USD", "symbol": "alUSD", "decimals": null, "network": "arbitrum"}, {"address": "0x1d987200df3b744cfa9c14f713f5334cb4bc4d5d", "name": "REKT", "symbol": "REKT", "decimals": null, "network": "arbitrum"}, {"address": "0x4e352cf164e64adcbad318c3a1e222e9eba4ce42", "name": "MCDEX Token", "symbol": "MCB", "decimals": null, "network": "arbitrum"}, {"address": "0xb2f30a7c980f052f02563fb518dcc39e6bf38175", "name": "Synthetix USD", "symbol": "USDx", "decimals": null, "network": "arbitrum"}, {"address": "0xe10d4a4255d2d35c9e23e2c4790e073046fbaf5c", "name": "LandX Governance Token", "symbol": "LNDX", "decimals": null, "network": "arbitrum"}, {"address": "0x93ca0d85837ff83158cd14d65b169cdb223b1921", "name": "Eclipse Fi", "symbol": "ECLIP", "decimals": null, "network": "arbitrum"}, {"address": "0xf7693c6fd9a7172d537fa75d133d309501cbd657", "name": "Web3 No Value", "symbol": "W3N", "decimals": null, "network": "arbitrum"}, {"address": "0xf0157af31dcbc774dc64580624fa502c024d4661", "name": "SMT", "symbol": "SMT", "decimals": null, "network": "arbitrum"}, {"address": "0xad4b9c1fbf4923061814dd9d5732eb703faa53d4", "name": "Wicrypt Network Token", "symbol": "WNT", "decimals": null, "network": "arbitrum"}, {"address": "0xde903e2712288a1da82942dddf2c20529565ac30", "name": "Swapr", "symbol": "SWPR", "decimals": null, "network": "arbitrum"}, {"address": "0x000f1720a263f96532d1ac2bb9cdc12b72c6f386", "name": "Fluidity", "symbol": "FLY", "decimals": null, "network": "arbitrum"}, {"address": "0x2e9a6df78e42a30712c10a9dc4b1c8656f8f2879", "name": "4d616b6572000000000000000000000000000000000000000000000000000000", "symbol": "4d4b520000000000000000000000000000000000000000000000000000000000", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "K", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Venus XVS", "symbol": "XVS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Aave Arbitrum WETH", "symbol": "aArbWETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Truflation", "symbol": "TRUF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GammaSwap", "symbol": "GS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Governance OHM", "symbol": "gOHM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "DORAB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Peapods", "symbol": "PEAS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Olympus", "symbol": "OHM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Kyber Network Crystal v2", "symbol": "KNC", "decimals": null, "network": "arbitrum"}, {"address": "0xc15319c5a0b98c9a2c908182c5f464ca1561e8c2", "name": "Puffverse", "symbol": "PFVS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Smolcoin", "symbol": "SMOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "The Standard USD", "symbol": "USDs", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "CoW Protocol Token", "symbol": "COW", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Coinbase Wrapped Staked ETH", "symbol": "cbETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Molten", "symbol": "MOLTEN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Usual USD", "symbol": "USD0", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "agEUR", "symbol": "agEUR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SeedifyFund", "symbol": "SFUND", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BOBOTRUM", "symbol": "BO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EdgeMatrix Computing network", "symbol": "EMC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "JoeToken", "symbol": "JOE", "decimals": null, "network": "arbitrum"}, {"address": "0x057ba66c2109fd4487f0781e0203b71fd77a6341", "name": "SERVICE", "symbol": "SRV", "decimals": null, "network": "arbitrum"}, {"address": "0x6fd58f5a2f3468e35feb098b5f59f04157002407", "name": "poor guy", "symbol": "pogai", "decimals": null, "network": "arbitrum"}, {"address": "0x354a6da3fcde098f8389cad84b0182725c6c91de", "name": "Compound", "symbol": "COMP", "decimals": null, "network": "arbitrum"}, {"address": "0xd420583dd2f1e53c41c3433be72b30202f5e51a3", "name": "XPER TOKEN", "symbol": "XPER", "decimals": null, "network": "arbitrum"}, {"address": "0xb0505e5a99abd03d94a1169e638b78edfed26ea4", "name": "<PERSON><PERSON> (Universal)", "symbol": "uSUI", "decimals": null, "network": "arbitrum"}, {"address": "0x45d9831d8751b2325f3dbf48db748723726e1c8c", "name": "EverValueCoin", "symbol": "EVA", "decimals": null, "network": "arbitrum"}, {"address": "0xf75ee6d319741057a82a88eeff1dbafab7307b69", "name": "Kryll", "symbol": "KRL", "decimals": null, "network": "arbitrum"}, {"address": "0x7fb7ede54259cb3d4e1eaf230c7e2b1ffc951e9a", "name": "NUMA", "symbol": "NUMA", "decimals": null, "network": "arbitrum"}, {"address": "0x3124678d62d2aa1f615b54525310fbfda6dcf7ae", "name": "<PERSON><PERSON>", "symbol": "SNSY", "decimals": null, "network": "arbitrum"}, {"address": "0xe086022290444bcd279d22c56925cc5eda389999", "name": "lucky money Arb", "symbol": "YSARB", "decimals": null, "network": "arbitrum"}, {"address": "0x82e64f49ed5ec1bc6e43dad4fc8af9bb3a2312ee", "name": "Aave Arbitrum DAI", "symbol": "aArbDAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "HOLD TOKEN", "symbol": "HOLD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "XNF", "symbol": "XNF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Kujira native asset", "symbol": "KUJI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Zunami ETH", "symbol": "zunETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USDFI", "symbol": "USDFI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Lockquidity", "symbol": "LOCK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Hop", "symbol": "HOP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USSD", "symbol": "USSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SPARTA", "symbol": "SPARTA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Crypto Index Pool", "symbol": "CIP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON>ana (Universal)", "symbol": "uSOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Vesta Stable", "symbol": "VST", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Synapse", "symbol": "SYN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "GRAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "RUBIC TOKEN", "symbol": "RBC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "FARE", "symbol": "FARE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "gFLY", "symbol": "GFLY", "decimals": null, "network": "arbitrum"}, {"address": "0xca5ca9083702c56b481d1eec86f1776fdbd2e594", "name": "Reserve Rights", "symbol": "RSR", "decimals": null, "network": "arbitrum"}, {"address": "0x10393c20975cf177a3513071bc110f7962cd67da", "name": "Jones DAO", "symbol": "JONES", "decimals": null, "network": "arbitrum"}, {"address": "0x4ac623237de0aa622b4fdf4da63cf97216371acf", "name": "alXAI", "symbol": "alXAI", "decimals": null, "network": "arbitrum"}, {"address": "0xc760f9782f8cea5b06d862574464729537159966", "name": "Contango", "symbol": "TANGO", "decimals": null, "network": "arbitrum"}, {"address": "0xc9b8a3fdecb9d5b218d02555a8baf332e5b740d5", "name": "Curve.fi Factory Plain Pool: FRAXBP", "symbol": "FRAXBP-f", "decimals": null, "network": "arbitrum"}, {"address": "0x8b5e4c9a188b1a187f2d1e80b1c2fb17fa2922e1", "name": "GoldenBoys", "symbol": "GOLD", "decimals": null, "network": "arbitrum"}, {"address": "0x2615a94df961278dcbc41fb0a54fec5f10a693ae", "name": "XRP (Universal)", "symbol": "uXRP", "decimals": null, "network": "arbitrum"}, {"address": "0xf19547f9ed24aa66b03c3a552d181ae334fbb8db", "name": "Lodestar", "symbol": "LODE", "decimals": null, "network": "arbitrum"}, {"address": "0xf197ffc28c23e0309b5559e7a166f2c6164c80aa", "name": "MXNB", "symbol": "MXNB", "decimals": null, "network": "arbitrum"}, {"address": "0xdd1ddd4d978ac0baef4bfa9c7e91853bfce90f11", "name": "The Lux Network", "symbol": "TLN", "decimals": null, "network": "arbitrum"}, {"address": "0x999999990237e901c537bbd768e09562be02efa5", "name": "Unishop.ai", "symbol": "Unishop.ai", "decimals": null, "network": "arbitrum"}, {"address": "0x09e18590e8f76b6cf471b3cd75fe1a1a9d2b2c2b", "name": "AIDOGE", "symbol": "AIDOGE", "decimals": null, "network": "arbitrum"}, {"address": "0x7b5eb3940021ec0e8e463d5dbb4b7b09a89ddf96", "name": "Wombat Token", "symbol": "WOM", "decimals": null, "network": "arbitrum"}, {"address": "0xe16e2548a576ad448fb014bbe85284d7f3542df5", "name": "Lumoz token", "symbol": "MOZ", "decimals": null, "network": "arbitrum"}, {"address": "0xccd05a0fcfc1380e9da27862adb2198e58e0d66f", "name": "<PERSON><PERSON>", "symbol": "ANIMA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Static Aave Arbitrum GHO", "symbol": "stataArbGHO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Osaka Protocol", "symbol": "OSAK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "XYRO", "symbol": "XYRO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer cbETH/rETH/wstETH CSP", "symbol": "cbETH/rETH/wstETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "iFARM", "symbol": "iFARM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ICHI", "symbol": "ICHI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "peg-eUSD", "symbol": "peUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "TAROT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Quick Intel", "symbol": "QKNTL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Frax Share", "symbol": "FXS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "OmniCat", "symbol": "OMNI", "decimals": null, "network": "arbitrum"}, {"address": "0x0c4681e6c0235179ec3d4f4fc4df3d14fdd96017", "name": "<PERSON><PERSON><PERSON>", "symbol": "RDNT", "decimals": null, "network": "arbitrum"}, {"address": "0x51318b7d00db7acc4026c88c3952b66278b6a67f", "name": "<PERSON><PERSON><PERSON>", "symbol": "PLS", "decimals": null, "network": "arbitrum"}, {"address": "0xf50874f8246776ca4b89eef471e718f70f38458f", "name": "Arbswap Token", "symbol": "ARBS", "decimals": null, "network": "arbitrum"}, {"address": "0x680447595e8b7b3aa1b43beb9f6098c79ac2ab3f", "name": "Decentralized USD", "symbol": "USDD", "decimals": null, "network": "arbitrum"}, {"address": "0xd3443ee1e91af28e5fb858fbd0d72a63ba8046e0", "name": "Gains Network USDC", "symbol": "gUSDC", "decimals": null, "network": "arbitrum"}, {"address": "0x06e90a57d1ece8752d6ce92d1ad348ead5eae4f4", "name": "Real Smurf Cat", "symbol": "ш<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "0x1509706a6c66ca549ff0cb464de88231ddbe213b", "name": "<PERSON>ra", "symbol": "AURA", "decimals": null, "network": "arbitrum"}, {"address": "0x2ac2b254bc18cd4999f64773a966e4f4869c34ee", "name": "<PERSON><PERSON>", "symbol": "PNP", "decimals": null, "network": "arbitrum"}, {"address": "0x6740acb82ac5c63a7ad2397ee1faed7c788f5f8c", "name": "AlienFi", "symbol": "ALIEN", "decimals": null, "network": "arbitrum"}, {"address": "0xde5ed76e7c05ec5e4572cfc88d1acea165109e44", "name": "DEUS", "symbol": "DEUS", "decimals": null, "network": "arbitrum"}, {"address": "0x451a3a936b26fa3f707958898bdb99640d958900", "name": "Block Cash", "symbol": "BKC", "decimals": null, "network": "arbitrum"}, {"address": "0x6c2c06790b3e3e3c38e12ee22f8183b37a13ee55", "name": "Dopex Governance Token", "symbol": "DPX", "decimals": null, "network": "arbitrum"}, {"address": "0x6db8b088c4d41d622b44cd81b900ba690f2d496c", "name": "IDIA", "symbol": "IDIA", "decimals": null, "network": "arbitrum"}, {"address": "0x643b34980e635719c15a2d4ce69571a258f940e9", "name": "The Standard EURO", "symbol": "EUROs", "decimals": null, "network": "arbitrum"}, {"address": "0x38f9bf9dce51833ec7f03c9dc218197999999999", "name": "<PERSON><PERSON>", "symbol": "NYA", "decimals": null, "network": "arbitrum"}, {"address": "0xdc8184ba488e949815d4aafb35b3c56ad03b4179", "name": "<PERSON><PERSON>", "symbol": "ROSX", "decimals": null, "network": "arbitrum"}, {"address": "0x2879e4cff83108042a3bd575b5ca46f5b230d073", "name": "<PERSON><PERSON>", "symbol": "PAMP", "decimals": null, "network": "arbitrum"}, {"address": "0x69420f9e38a4e60a62224c489be4bf7a94402496", "name": "Defi.Money Stablecoin", "symbol": "MONEY", "decimals": null, "network": "arbitrum"}, {"address": "0xd6cf874e24a9f5f43075142101a6b13735cdd424", "name": "CBPAY", "symbol": "CBPAY", "decimals": null, "network": "arbitrum"}, {"address": "0x74ccbe53f77b08632ce0cb91d3a545bf6b8e0979", "name": "Fantom Bomb", "symbol": "fBOMB", "decimals": null, "network": "arbitrum"}, {"address": "0x155f0dd04424939368972f4e1838687d6a831151", "name": "ArbiDoge", "symbol": "ADoge", "decimals": null, "network": "arbitrum"}, {"address": "0x1c986661170c1834db49c3830130d4038eeeb866", "name": "Aperture", "symbol": "APTR", "decimals": null, "network": "arbitrum"}, {"address": "0xbbea044f9e7c0520195e49ad1e561572e7e1b948", "name": "MZR", "symbol": "MZR Token", "decimals": null, "network": "arbitrum"}, {"address": "0x83e60b9f7f4db5cdb0877659b1740e73c662c55b", "name": "<PERSON><PERSON>", "symbol": "PINGU", "decimals": null, "network": "arbitrum"}, {"address": "0x4cfa50b7ce747e2d61724fcac57f24b748ff2b2a", "name": "Fluid USDC", "symbol": "fUSDC", "decimals": null, "network": "arbitrum"}, {"address": "0x1d1498166ddceee616a6d99868e1e0677300056f", "name": "xSpace Token", "symbol": "xSPACE", "decimals": null, "network": "arbitrum"}, {"address": "0xf42e2b8bc2af8b110b65be98db1321b1ab8d44f5", "name": "Donut", "symbol": "DONUT", "decimals": null, "network": "arbitrum"}, {"address": "0x296a0b8847bd4ed9af71a9ef238fa5be0778b611", "name": "ATLAS", "symbol": "ATA", "decimals": null, "network": "arbitrum"}, {"address": "0x01ec17d90d4f4e4cce1b602d461c8d4d52e96f46", "name": "Phoenix Token", "symbol": "PX", "decimals": null, "network": "arbitrum"}, {"address": "0xd07d35368e04a839dee335e213302b21ef14bb4a", "name": "Crystal", "symbol": "CRYSTAL", "decimals": null, "network": "arbitrum"}, {"address": "0xc578d2823224c860d5561426925f81d805992455", "name": "FINX Governance Token", "symbol": "FINX", "decimals": null, "network": "arbitrum"}, {"address": "0x666966ef3925b1c92fa355fda9722899f3e73451", "name": "STABLE", "symbol": "STABLE", "decimals": null, "network": "arbitrum"}, {"address": "0xd8e9e0ed6f6782cbfee67a1c25c5e514901f4d89", "name": "<PERSON><PERSON><PERSON>", "symbol": "ARA", "decimals": null, "network": "arbitrum"}, {"address": "0xbe00f3db78688d9704bcb4e0a827aea3a9cc0d62", "name": "Fiat24 USD", "symbol": "USD24", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USD0 Liquid Bond", "symbol": "USD0++", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped stLINK", "symbol": "wstLINK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "AISHIB", "symbol": "AISHIB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Solv BTC", "symbol": "SolvBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "ARC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Tessellate", "symbol": "TSL8", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ArbiNYAN", "symbol": "NYAN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Zyber Token", "symbol": "ZYB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Alchemix ETH", "symbol": "alETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Staked agEUR", "symbol": "stEUR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Curve.fi USDC/USDT", "symbol": "2CRV", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LUSD Stablecoin", "symbol": "LUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "HeartCoin", "symbol": "HTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Vesta", "symbol": "VSTA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Glo Dollar", "symbol": "USDGLO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "DropArb", "symbol": "DROP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "LDY", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Rabbit", "symbol": "RAB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Pentagon", "symbol": "PEN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Liquid Staked ETH", "symbol": "LSETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "RYZE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "LADYS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "YHDL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Graph <PERSON>", "symbol": "GRT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BRUH", "symbol": "BRUH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SolidLizard dex token", "symbol": "SLIZ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Synonym Finance", "symbol": "SYNO", "decimals": null, "network": "arbitrum"}, {"address": "0x640278bada847b7ce71bb22f20517a009a049640", "name": "NGT", "symbol": "NGT", "decimals": null, "network": "arbitrum"}, {"address": "0xb21be1caf592a5dc1e75e418704d1b6d50b0d083", "name": "CORTEX", "symbol": "CRX", "decimals": null, "network": "arbitrum"}, {"address": "0x4d22e37eb4d71d1acc5f4889a65936d2a44a2f15", "name": "Hat", "symbol": "HAT", "decimals": null, "network": "arbitrum"}, {"address": "0x338c693545841819043ed8aab4959ca67dce8ecf", "name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "0x221a0f68770658c15b525d0f89f5da2baab5f321", "name": "Open Dollar", "symbol": "OD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "AYIN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Possum", "symbol": "PSM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Standard Token", "symbol": "TST", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "unshETHing_Token", "symbol": "USH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "OX Coin", "symbol": "OX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Decentralized Mining and Hosting Service", "symbol": "DMHS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "MOZ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SharkySwap", "symbol": "SHARKY", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Moons", "symbol": "MOON", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Trident", "symbol": "PSI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "CHRONOS", "symbol": "CHR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "OreoSwap", "symbol": "OREO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "D2", "symbol": "D2", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Staked ETH", "symbol": "osETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "MIRTH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Black Agnus", "symbol": "FTW", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Nutcash", "symbol": "NCASH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Equilibria Pendle", "symbol": "<PERSON><PERSON><PERSON><PERSON>", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Astra DAO", "symbol": "ASTRADAO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GOAT", "symbol": "GOA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Rocket Pool Protocol", "symbol": "RPL", "decimals": null, "network": "arbitrum"}, {"address": "0x000000000026839b3f4181f2cf69336af6153b99", "name": "GG", "symbol": "GG", "decimals": null, "network": "arbitrum"}, {"address": "0xee0b14e8fc86691cf6ee42b9954985b4cf968534", "name": "ZenPanda<PERSON>oin", "symbol": "ZPC", "decimals": null, "network": "arbitrum"}, {"address": "0x6609be1547166d1c4605f3a243fdcff467e600c3", "name": "NEU", "symbol": "NEU", "decimals": null, "network": "arbitrum"}, {"address": "0x1dd6b5f9281c6b4f043c02a83a46c2772024636c", "name": "Lumi Finance USD", "symbol": "LUAUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MAGICToken", "symbol": "MAGIC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Impossible Decentralized Incubator Access Token", "symbol": "IDIA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Staked Frax Ether", "symbol": "sfrxETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SmarDex Token", "symbol": "SDEX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Cocaine", "symbol": "COKE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "PoolTogether", "symbol": "POOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BITCOIN MISSION", "symbol": "BTCM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Saving USX", "symbol": "sUSX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Aladdin sdCRV", "symbol": "asdCRV", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Elk", "symbol": "ELK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON> Wrapped KNC", "symbol": "axlKNC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Florence Finance Medici", "symbol": "FFM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped OETH", "symbol": "WOETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Sector", "symbol": "SECT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "dForce", "symbol": "DF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "SCHRODI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "uniBTC", "symbol": "uniBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Poison.Finance Poison", "symbol": "POI$ON", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Level Token", "symbol": "LVL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GMD", "symbol": "GMD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "OpenOcean", "symbol": "OOE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Quantlytica <PERSON>", "symbol": "QTLX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "O3 Swap Token", "symbol": "O3", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "The Doge NFT", "symbol": "DOG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EthereumMax", "symbol": "EMAX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GOVI", "symbol": "GOVI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Volta Protocol Token", "symbol": "VOLTA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Size", "symbol": "SIZE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Banania", "symbol": "BANANIA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Badger", "symbol": "BADGER", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "UMAMI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MYRC", "symbol": "MYRC", "decimals": null, "network": "arbitrum"}, {"address": "0x2a5c6db79e1a03f932e92d4998d8d42f6506e76c", "name": "ARBS", "symbol": "ARBS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped TAO", "symbol": "wTAO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "AGI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "VelaToken", "symbol": "VELA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Alongside Crypto Market Index", "symbol": "AMKT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Y2K", "symbol": "Y2K", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USDV", "symbol": "USDV", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "CURVEBOBR", "symbol": "BobrCRV", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Astrinea Gold", "symbol": "ASTGO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "TOOB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Equation", "symbol": "EQU", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Connect Financial", "symbol": "CNFI", "decimals": null, "network": "arbitrum"}, {"address": "0x0d111e482146fe9ac9ca3a65d92e65610bbc1ba6", "name": "Plutus SPA", "symbol": "plsSPA", "decimals": null, "network": "arbitrum"}, {"address": "0x9cce9ae579142e372a8959285e3a5a2e211904f7", "name": "DGWToken", "symbol": "DGW", "decimals": null, "network": "arbitrum"}, {"address": "0xa2f9ecf83a48b86265ff5fd36cdbaaa1f349916c", "name": "Goons of Balatroon", "symbol": "GOB", "decimals": null, "network": "arbitrum"}, {"address": "0x03d283990dcc7b6ec59c442a0f7ff4b902a88769", "name": "NUMA", "symbol": "apNUMA", "decimals": null, "network": "arbitrum"}, {"address": "0xb285f136ba6be422125407aad565eeb8c6842fb7", "name": "The Legend Of Rufus", "symbol": "TLOR", "decimals": null, "network": "arbitrum"}, {"address": "0x7448c7456a97769f6cd04f1e83a4a23ccdc46abd", "name": "Maver<PERSON>", "symbol": "MAV", "decimals": null, "network": "arbitrum"}, {"address": "0x2598c30330d5771ae9f983979209486ae26de875", "name": "Any Inu", "symbol": "AI", "decimals": null, "network": "arbitrum"}, {"address": "0xc9bd1c1e65ebfb36cf4b3d9fc8e2b844248deee8", "name": "Kudai", "symbol": "KUDAI", "decimals": null, "network": "arbitrum"}, {"address": "0x7f9a7db853ca816b9a138aee3380ef34c437dee0", "name": "Gitcoin", "symbol": "GTC", "decimals": null, "network": "arbitrum"}, {"address": "0xc628534100180582e43271448098cb2c185795bd", "name": "Flashstake", "symbol": "FLASH", "decimals": null, "network": "arbitrum"}, {"address": "0x9e758b8a98a42d612b3d38b66a22074dc03d7370", "name": "Symbiosis", "symbol": "SIS", "decimals": null, "network": "arbitrum"}, {"address": "0xcbe94d75ec713b7ead84f55620dc3174beeb1cfe", "name": "FORE Protocol", "symbol": "FORE", "decimals": null, "network": "arbitrum"}, {"address": "0x2cab3abfc1670d1a452df502e216a66883cdf079", "name": "Layer2DAO", "symbol": "L2DAO", "decimals": null, "network": "arbitrum"}, {"address": "0x412f4b922bb9afdc65c7e9b669f4bec6e554452a", "name": "RepubliK", "symbol": "RPK", "decimals": null, "network": "arbitrum"}, {"address": "0xdb298285fe4c5410b05390ca80e8fbe9de1f259b", "name": "handleFOREX", "symbol": "FOREX", "decimals": null, "network": "arbitrum"}, {"address": "0x569deb225441fd18bde18aed53e2ec7eb4e10d93", "name": "YFX", "symbol": "YFX", "decimals": null, "network": "arbitrum"}, {"address": "0x8697841b82c71fcbd9e58c15f6de68cd1c63fd02", "name": "Nutcoin", "symbol": "NUT", "decimals": null, "network": "arbitrum"}, {"address": "0x2c110867ca90e43d372c1c2e92990b00ea32818b", "name": "Stabilize Token", "symbol": "STBZ", "decimals": null, "network": "arbitrum"}, {"address": "0x6a7661795c374c0bfc635934efaddff3a7ee23b6", "name": "Dola USD Stablecoin", "symbol": "DOLA", "decimals": null, "network": "arbitrum"}, {"address": "0x65c101e95d7dd475c7966330fa1a803205ff92ab", "name": "Hall of Legends", "symbol": "HOL", "decimals": null, "network": "arbitrum"}, {"address": "0x6bcc14b02cd624ebe1a8a665cb6d4067aa097230", "name": "Foxify", "symbol": "FOX", "decimals": null, "network": "arbitrum"}, {"address": "0xbad58ed9b5f26a002ea250d7a60dc6729a4a2403", "name": "Paribus", "symbol": "PBX", "decimals": null, "network": "arbitrum"}, {"address": "0x5db7b150c5f38c5f5db11dcbdb885028fcc51d68", "name": "<PERSON>", "symbol": "STR", "decimals": null, "network": "arbitrum"}, {"address": "0x978cd62ed2d247e4b72c7df07484b8ec6ce92c9c", "name": "Insurance Capital Trust", "symbol": "ICT", "decimals": null, "network": "arbitrum"}, {"address": "0xfb9e5d956d889d91a82737b9bfcdac1dce3e1449", "name": "LQTY", "symbol": "LQTY", "decimals": null, "network": "arbitrum"}, {"address": "0x8bc65eed474d1a00555825c91feab6a8255c2107", "name": "Balancer DOLA/USDC StablePool", "symbol": "DOLA/USDC BPT", "decimals": null, "network": "arbitrum"}, {"address": "0x99c409e5f62e4bd2ac142f17cafb6810b8f0baae", "name": "beefy.finance", "symbol": "BIFI", "decimals": null, "network": "arbitrum"}, {"address": "0xdd89a4d3362828e601df52208302a741f08e46c5", "name": "O3", "symbol": "O3", "decimals": null, "network": "arbitrum"}, {"address": "0xb348b87b23d5977e2948e6f36ca07e1ec94d7328", "name": "SwapFish", "symbol": "FISH", "decimals": null, "network": "arbitrum"}, {"address": "0x58cb98a966f62aa6f2190eb3aa03132a0c3de3d5", "name": "OpenWorld", "symbol": "OPEN", "decimals": null, "network": "arbitrum"}, {"address": "0xf8388c2b6edf00e2e27eef5200b1befb24ce141d", "name": "<PERSON><PERSON>", "symbol": "NOLA", "decimals": null, "network": "arbitrum"}, {"address": "0xa992b7dde2ed1632d0b66c56744e914ed673a37f", "name": "AGIOToken", "symbol": "AGIO", "decimals": null, "network": "arbitrum"}, {"address": "0xf525e73bdeb4ac1b0e741af3ed8a8cbb43ab0756", "name": "KiboShib", "symbol": "KIBSHI", "decimals": null, "network": "arbitrum"}, {"address": "0x092baadb7def4c3981454dd9c0a0d7ff07bcfc86", "name": "MOR", "symbol": "MOR", "decimals": null, "network": "arbitrum"}, {"address": "0x86f65121804d2cdbef79f9f072d4e0c2eebabc08", "name": "SEED", "symbol": "SEED", "decimals": null, "network": "arbitrum"}, {"address": "0xef261714f7e5ba6b86f4780eb6e3bf26b10729cf", "name": "White Lotus", "symbol": "LOTUS", "decimals": null, "network": "arbitrum"}, {"address": "0xeb1a8f8ea373536600082ba9ae2db97327513f7d", "name": "OHM Arbitrum Pod", "symbol": "apOHM", "decimals": null, "network": "arbitrum"}, {"address": "0xddbfbd5dc3ba0feb96cb513b689966b2176d4c09", "name": "STEAK", "symbol": "STEAK", "decimals": null, "network": "arbitrum"}, {"address": "0xacc51ffdef63fb0c014c882267c3a17261a5ed50", "name": "StrykeToken", "symbol": "SYK", "decimals": null, "network": "arbitrum"}, {"address": "0x00000000000451f49c692bfc24971cacea2db678", "name": "<PERSON><PERSON>", "symbol": "HERMES", "decimals": null, "network": "arbitrum"}, {"address": "0x24305e6911cf5bc5241347befc7a322314bf4aec", "name": "Peapods Silo USD flatcoin", "symbol": "apUSD", "decimals": null, "network": "arbitrum"}, {"address": "0x1824a51c106efc27d35a74efb56d9bf54ddb22d4", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "PRY", "decimals": null, "network": "arbitrum"}, {"address": "0x21e60ee73f17ac0a411ae5d690f908c3ed66fe12", "name": "<PERSON><PERSON>", "symbol": "DERI", "decimals": null, "network": "arbitrum"}, {"address": "0x8d7c2588c365b9e98ea464b63dbccdf13ecd9809", "name": "Flourishing AI Token", "symbol": "AI", "decimals": null, "network": "arbitrum"}, {"address": "0x56659245931cb6920e39c189d2a0e7dd0da2d57b", "name": "Impermax", "symbol": "IBEX", "decimals": null, "network": "arbitrum"}, {"address": "0xb1084db8d3c05cebd5fa9335df95ee4b8a0edc30", "name": "USDT+", "symbol": "USDT+", "decimals": null, "network": "arbitrum"}, {"address": "0x77b7787a09818502305c95d68a2571f090abb135", "name": "Derive", "symbol": "DRV", "decimals": null, "network": "arbitrum"}, {"address": "0xaae0c3856e665ff9b3e2872b6d75939d810b7e40", "name": "YieldFarming Index", "symbol": "YFX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped <PERSON><PERSON>", "symbol": "wBAN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped BGL", "symbol": "WBGL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped Mountain Protocol USD", "symbol": "wUSDM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "dlcBTC", "symbol": "DLCBTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fanko Official Token", "symbol": "raz", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "AUSD Token", "symbol": "AUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "AXL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "HedgePlus", "symbol": "HedgePlus", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Crypto Volatility Token", "symbol": "CVOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Ya<PERSON>", "symbol": "YAK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "WISELIQUID", "symbol": "WLC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "xSHRAP", "symbol": "xSHRAP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "TRUST AI", "symbol": "TRT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Autonolas", "symbol": "OLAS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "neadRAM: Tokenized veRAM", "symbol": "neadRAM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ArbiRoul Casino Chip", "symbol": "ROUL", "decimals": null, "network": "arbitrum"}, {"address": "0x83e5ecd192eac043b0674a16eedf96176726a159", "name": "Redacted", "symbol": "RED", "decimals": null, "network": "arbitrum"}, {"address": "0x27f485b62c4a7e635f561a87560adf5090239e93", "name": "DFX Token (L2)", "symbol": "DFX", "decimals": null, "network": "arbitrum"}, {"address": "0x94025780a1ab58868d9b2dbbb775f44b32e8e6e5", "name": "BetSwirl v2", "symbol": "BETS", "decimals": null, "network": "arbitrum"}, {"address": "0x431402e8b9de9aa016c743880e04e517074d8cec", "name": "<PERSON><PERSON>", "symbol": "HEGIC", "decimals": null, "network": "arbitrum"}, {"address": "0x3b58a4c865b568a2f6a957c264f6b50cba35d8ce", "name": "SuperWalk", "symbol": "GRND", "decimals": null, "network": "arbitrum"}, {"address": "0x9d0c0675a995d5f12b03e880763f639d0628b5c6", "name": "Walk Token", "symbol": "WALK", "decimals": null, "network": "arbitrum"}, {"address": "0x6688b00f0c23a4a546beaae51a7c90c439895d48", "name": "<PERSON><PERSON>", "symbol": "TAROT", "decimals": null, "network": "arbitrum"}, {"address": "0xa9004a5421372e1d83fb1f85b0fc986c912f91f3", "name": "Wrapped BNB", "symbol": "WBNB", "decimals": null, "network": "arbitrum"}, {"address": "0xdae6c6b6896abe615c6fff074a6bc4458eb27c40", "name": "Aegis token", "symbol": "AEG", "decimals": null, "network": "arbitrum"}, {"address": "0x20d419a8e12c45f88fda7c5760bb6923cee27f98", "name": "ostiumLP", "symbol": "oLP", "decimals": null, "network": "arbitrum"}, {"address": "0xadd5620057336f868eae78a451c503ae7b576bad", "name": "noiseGPT", "symbol": "noiseGPT", "decimals": null, "network": "arbitrum"}, {"address": "0x079504b86d38119f859c4194765029f692b7b7aa", "name": "<PERSON><PERSON>", "symbol": "LYRA", "decimals": null, "network": "arbitrum"}, {"address": "0x3419875b4d3bca7f3fdda2db7a476a79fd31b4fe", "name": "DizzyHavoc", "symbol": "DZHV", "decimals": null, "network": "arbitrum"}, {"address": "0xa61f74247455a40b01b0559ff6274441fafa22a3", "name": "<PERSON><PERSON><PERSON>", "symbol": "MGP", "decimals": null, "network": "arbitrum"}, {"address": "0x00e1724885473b63bce08a9f0a52f35b0979e35a", "name": "Oath <PERSON>ken", "symbol": "OATH", "decimals": null, "network": "arbitrum"}, {"address": "0xf0d43f46cea02bbb5e616bf6d795d4f8719cd80d", "name": "Zeleboba Token", "symbol": "ZELEBOBA", "decimals": null, "network": "arbitrum"}, {"address": "0x88f3c911190a2b6459345872c363371f3dfa72b6", "name": "<PERSON><PERSON>", "symbol": "HAMBURGLAR", "decimals": null, "network": "arbitrum"}, {"address": "0xa0b862f60edef4452f25b4160f177db44deb6cf1", "name": "Gnosis <PERSON>", "symbol": "GNO", "decimals": null, "network": "arbitrum"}, {"address": "0xb01cf1be9568f09449382a47cd5bf58e2a9d5922", "name": "LightSpeed", "symbol": "Speed", "decimals": null, "network": "arbitrum"}, {"address": "0xac800fd6159c2a2cb8fc31ef74621eb430287a5a", "name": "Optimism", "symbol": "OP", "decimals": null, "network": "arbitrum"}, {"address": "0x437cc33344a0b27a429f795ff6b469c72698b291", "name": "WrappedM by M^0", "symbol": "wM", "decimals": null, "network": "arbitrum"}, {"address": "0x894341be568eae3697408c420f1d0acfce6e55f9", "name": "FUSD", "symbol": "FUSD", "decimals": null, "network": "arbitrum"}, {"address": "0x3858567501fbf030bd859ee831610fcc710319f4", "name": "Nexade", "symbol": "NEXD", "decimals": null, "network": "arbitrum"}, {"address": "0xda51015b73ce11f77a115bb1b8a7049e02ddecf0", "name": "Neutra Token", "symbol": "NEU", "decimals": null, "network": "arbitrum"}, {"address": "0xdd4ab36f31891ff25a69121fccc732d93eb976dd", "name": "Prime", "symbol": "D2D", "decimals": null, "network": "arbitrum"}, {"address": "0x561877b6b3dd7651313794e5f2894b2f18be0766", "name": "<PERSON><PERSON>", "symbol": "MATIC", "decimals": null, "network": "arbitrum"}, {"address": "0x12275dcb9048680c4be40942ea4d92c74c63b844", "name": "Electronic Dollar", "symbol": "eUSD", "decimals": null, "network": "arbitrum"}, {"address": "0x27b15ee5391ad7d3770ae0cfa157f61c48a27a0b", "name": "ORSO", "symbol": "ORSO", "decimals": null, "network": "arbitrum"}, {"address": "0xef4a1d459d62dfd2ebb9c45b04f90f0a7ba1d56e", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "0xe1d3495717f9534db67a6a8d4940dd17435b6a9e", "name": "Locus <PERSON>", "symbol": "LOCUS", "decimals": null, "network": "arbitrum"}, {"address": "0x64081252c497fcfec247a664e9d10ca8ed71b276", "name": "ArbiFLUX", "symbol": "ArbiFLUX", "decimals": null, "network": "arbitrum"}, {"address": "0x03f14957f1944aa9139366b28a3b91e5f46e5dae", "name": "AppleTree", "symbol": "APTESG", "decimals": null, "network": "arbitrum"}, {"address": "0x2823f231b8b7121c4ba6b6c0ceef37b6a5bda547", "name": "AICODE v2", "symbol": "AICODE", "decimals": null, "network": "arbitrum"}, {"address": "0xebd72d1ad20530d18f6b63dbc763dad8922952ba", "name": "AIR", "symbol": "AIR", "decimals": null, "network": "arbitrum"}, {"address": "0x2f800db57b67e7c5216e76590d45b9b0b0bd7dcb", "name": "SLURP", "symbol": "$SLURP", "decimals": null, "network": "arbitrum"}, {"address": "0x934f5a826666faaeb6d6d7258cbfe378bcf20d6d", "name": "EIDON AI", "symbol": "EDAI", "decimals": null, "network": "arbitrum"}, {"address": "0x6658394ca08b855de82e15a8002a8d1c4f0d05d9", "name": "Mayor <PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "McCHEESE", "decimals": null, "network": "arbitrum"}, {"address": "0x74885b4d524d497261259b38900f54e6dbad2210", "name": "ApeCoin", "symbol": "APE", "decimals": null, "network": "arbitrum"}, {"address": "0x69a1e699f562d7af66fc6cc473d99f4430c3acd2", "name": "Param", "symbol": "PARAM", "decimals": null, "network": "arbitrum"}, {"address": "0x27b58d226fe8f792730a795764945cf146815aa7", "name": "Alchemix", "symbol": "ALCX", "decimals": null, "network": "arbitrum"}, {"address": "0x4502ca3f34f9a495e2df81b95d1bf1965c3c47e6", "name": "ZY Token", "symbol": "ZY", "decimals": null, "network": "arbitrum"}, {"address": "0x6491c05a82219b8d1479057361ff1654749b876b", "name": "USDS Stablecoin", "symbol": "USDS", "decimals": null, "network": "arbitrum"}, {"address": "0x73db621cc8f0a767bed0e5c0a2920f480d77b765", "name": "GTA", "symbol": "GTA", "decimals": null, "network": "arbitrum"}, {"address": "0x34710897c0492b3869adb61ac417b39d2a9ec481", "name": "<PERSON><PERSON>", "symbol": "ARBS", "decimals": null, "network": "arbitrum"}, {"address": "0xcbd34be8b8deb2fb6f5cc5bc14a37c2398db5320", "name": "Funicular", "symbol": "FUN", "decimals": null, "network": "arbitrum"}, {"address": "0x4f137f5b3756cc46b0fafc62bb47b4c84e6cef68", "name": "<PERSON><PERSON>", "symbol": "BIRDIE", "decimals": null, "network": "arbitrum"}, {"address": "0x28bc3f91e153cffaf7f8c895784a8ea3e1730bf3", "name": "GEMS", "symbol": "GEMS", "decimals": null, "network": "arbitrum"}, {"address": "0xcf1d7d1a7bbf5d11ccfe717fc6a9a45c8dd9c7be", "name": "InterlockNetwork", "symbol": "ILOCK", "decimals": null, "network": "arbitrum"}, {"address": "0xbc33b4d48f76d17a1800afcb730e8a6aaada7fe5", "name": "Bifrost Voucher DOT", "symbol": "vDOT", "decimals": null, "network": "arbitrum"}, {"address": "0x83e1d2310ade410676b1733d16e89f91822fd5c3", "name": "Jito Staked SOL", "symbol": "JitoSOL", "decimals": null, "network": "arbitrum"}, {"address": "0xdee46be9d0b207e5d88d2efd84a045e725a242f7", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "0x7424f00845777a06e21f0bd8873f814a8a814b2d", "name": "Wrapped XTZ", "symbol": "WXTZ", "decimals": null, "network": "arbitrum"}, {"address": "0x9d0bfe46891573c12021942c7d28f15ebb641988", "name": "SparkPoint", "symbol": "SRK", "decimals": null, "network": "arbitrum"}, {"address": "0x2e412435928efe43b156caa8f4b1068729fee275", "name": "King <PERSON>", "symbol": "KING", "decimals": null, "network": "arbitrum"}, {"address": "0x1310952bc5594852459ee45bfd0df70b34ac5509", "name": "<PERSON><PERSON><PERSON>", "symbol": "PRF", "decimals": null, "network": "arbitrum"}, {"address": "0xa3ab90c49c2e7c873fb047d30cef87464ba1cb59", "name": "xBlast Token", "symbol": "XBL", "decimals": null, "network": "arbitrum"}, {"address": "0x8cf7e3aa6faf6ae180e5ec3f0fb95081c2086ebe", "name": "SX Network", "symbol": "SX", "decimals": null, "network": "arbitrum"}, {"address": "0xab8ebcc9eecc20bd30c7b75c7b4e8fcccfbf01ab", "name": "Ice", "symbol": "ICE", "decimals": null, "network": "arbitrum"}, {"address": "0x938d8eacd7637b6f94303d3728aec20c790a7c94", "name": "<PERSON><PERSON><PERSON>", "symbol": "AKITA", "decimals": null, "network": "arbitrum"}, {"address": "0x51c601dc278eb2cfea8e52c4caa35b3d6a9a2c26", "name": "Chainge", "symbol": "XCHNG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Bluwhale Points Token", "symbol": "BLUP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "YieldFi yToken", "symbol": "yUSD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MGTAI Token", "symbol": "MGTAI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SolvBTC Ethena", "symbol": "SolvBTC.ENA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Camelot GRAIL pod", "symbol": "apGRAIL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "TWTS", "symbol": "TWTS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer ankrETH/wstETH StablePool", "symbol": "ankrETH/wstETH-BPT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "PEN", "symbol": "PEN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Tulip Game", "symbol": "TLIP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Bamboo139", "symbol": "B139", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Staked USDe", "symbol": "sUSDe", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer sUSDe/USDC", "symbol": "sUSDe/USDC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>s", "symbol": "JOSS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MARQUEE", "symbol": "MARQ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "agETHWrapper", "symbol": "agETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EdgeMatrix Computing Pod", "symbol": "apEMC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LuxWorld", "symbol": "LUX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EDU Coin", "symbol": "EDU", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Tether USD", "symbol": "USDT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Arbitrum Ecosystem Thread Hash", "symbol": "ETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "VOL", "symbol": "VOL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "YOGEN.FUN", "symbol": "YOGEN", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ArbiRiseFinance", "symbol": "ARF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fractional ETH", "symbol": "fETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Leveraged ETH", "symbol": "xETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Aura BAL", "symbol": "auraBAL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "0Fx Protocol", "symbol": "0FX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fantom <PERSON>", "symbol": "FTM", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "2BTC", "symbol": "2BTC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "LfgSwap Finance Token", "symbol": "LFG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Henez", "symbol": "HEZ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "A<PERSON><PERSON>", "symbol": "ARS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fere AI", "symbol": "FERE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BEAGLE INU", "symbol": "BEAGLE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GPT", "symbol": "GPT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "EBONDS", "symbol": "EBONDS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "liquid ETH", "symbol": "lqETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Fair ARB Token", "symbol": "FARB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Eternal Cash", "symbol": "EC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Status Network Token", "symbol": "SNT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Zombie Virus Token", "symbol": "aZVT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer sUSX/USDC", "symbol": "sUSX/USDC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Plexus", "symbol": "PLX", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "PEASPod", "symbol": "apPEAS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Odes<PERSON>", "symbol": "ODS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Total NFT Market Cap", "symbol": "JPEGz", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Stake DAO Token", "symbol": "SDT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "GUDoge", "symbol": "GUDoge", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Wrapped PCI", "symbol": "WPCI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer Stable 4pool", "symbol": "4POOL-BPT", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Rai Reflex Index", "symbol": "RAI", "decimals": null, "network": "arbitrum"}, {"address": "0x85564af67760063b994599b640552614238c9ee6", "name": "Balancer USDX/USDT", "symbol": "USDX/USDT", "decimals": null, "network": "arbitrum"}, {"address": "0xf3527ef8de265eaa3716fb312c12847bfba66cef", "name": "USDX", "symbol": "USDX", "decimals": null, "network": "arbitrum"}, {"address": "0xa2235d059f80e176d931ef76b6c51953eb3fbef4", "name": "Ape USD", "symbol": "ApeUSD", "decimals": null, "network": "arbitrum"}, {"address": "0xa54b8e178a49f8e5405a4d44bb31f496e5564a05", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "0x35e6a59f786d9266c7961ea28c7b768b33959cbb", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "0xc604c6c646bb7a49d5829fbc479bb7b1b4da17f3", "name": "PEAS pod with USDC", "symbol": "apPEASUSDC", "decimals": null, "network": "arbitrum"}, {"address": "0x933d31561e470478079feb9a6dd2691fad8234df", "name": "Ocean Token", "symbol": "OCEAN", "decimals": null, "network": "arbitrum"}, {"address": "0xf877500c6ff3cf8305245bcb3cf1c5a6b7287aef", "name": "Bifrost Native Token", "symbol": "BNC", "decimals": null, "network": "arbitrum"}, {"address": "0x1f53b7aa6f4b36b7dfdedb4bc4a14747a19cf7b1", "name": "Apu Apus<PERSON>ja", "symbol": "APU", "decimals": null, "network": "arbitrum"}, {"address": "0x1dc687bf46c3b8faa1c1faf075be5d5055c5c136", "name": "Moon Tropica", "symbol": "CAH", "decimals": null, "network": "arbitrum"}, {"address": "0xb165a74407fe1e519d6bcbdec1ed3202b35a4140", "name": "Static Aave Arbitrum USDT", "symbol": "stataArbUSDT", "decimals": null, "network": "arbitrum"}, {"address": "0xc3c802b97c7c9c0fed4b330be0ccd3b2d06f956d", "name": "PeaOhmMax", "symbol": "apPmax", "decimals": null, "network": "arbitrum"}, {"address": "0x8c75a1c86c21b74754fc8e3bc4e7f79b4fcc5a28", "name": "ESIR", "symbol": "ESIR", "decimals": null, "network": "arbitrum"}, {"address": "0x04e539b978a897e1033fad55d749f01713c6d288", "name": "Empyreal w PEAS pair pod", "symbol": "apEMPxPEAS", "decimals": null, "network": "arbitrum"}, {"address": "0x05905af7933f89280ab258919f0dfa056ced8e43", "name": "Clutch", "symbol": "CLUTCH", "decimals": null, "network": "arbitrum"}, {"address": "0x7f1bce4691e187d56a3ec2e448c3f9112c1c2b4c", "name": "TurboGlobal", "symbol": "TGB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "STEAD", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Revenant", "symbol": "GAMEFI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Seed Of The Universe", "symbol": "SOTU", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Ethereum Name Service", "symbol": "ENS", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON><PERSON>i pepe", "symbol": "PEPE", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Q Governance Token", "symbol": "WQ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Plankton", "symbol": "PLNKTON", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Lodestar USD Coin", "symbol": "lUSDC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BUFF", "symbol": "BUFF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SHRAPNEL", "symbol": "SHRP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Balancer osETH/wETH", "symbol": "osETH/wETH", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "AICAP", "symbol": "AICAP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON>", "symbol": "kitty", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "SHEKEL", "symbol": "SHEKEL", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Tif", "symbol": "TIF", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "XDRAGON", "symbol": "XDRAGON", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "USDC Token", "symbol": "m.USDC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Pyth Network", "symbol": "PYTH", "decimals": null, "network": "arbitrum"}, {"address": "0x98ee5bff5b1aff288f91160f798ff331fb945d4f", "name": "Toil", "symbol": "Toil", "decimals": null, "network": "arbitrum"}, {"address": "0xd9e2123ab94e2491eafa57d709a12ccf9c49b5ec", "name": "OHMIE PEAS", "symbol": "apUMP", "decimals": null, "network": "arbitrum"}, {"address": "0x5431136817a5c3449f3057f8778bd971d8e48529", "name": "Insane Technology Index", "symbol": "ITX", "decimals": null, "network": "arbitrum"}, {"address": "0x3d48ae69a2f35d02d6f0c5e84cfe66be885f3963", "name": "<PERSON><PERSON><PERSON>", "symbol": "VIDYA", "decimals": null, "network": "arbitrum"}, {"address": "0x719a06acbe535b85ccb8c9a2b59cab9f8b9bce57", "name": "<PERSON><PERSON><PERSON>", "symbol": "GenZ", "decimals": null, "network": "arbitrum"}, {"address": "0x9ac3141c31d4be5cce0556a7a3e0b035adb32bad", "name": "Bunny Fu FU", "symbol": "BFF", "decimals": null, "network": "arbitrum"}, {"address": "0x6f9f4123cea73d09dc0bc96063e17d6a2de982e1", "name": "MK", "symbol": "MK", "decimals": null, "network": "arbitrum"}, {"address": "0x0fd8a8c3a89f36c87efa3dbb7d28c7781cdb3372", "name": "Arbitrum Basis Cash", "symbol": "ABC", "decimals": null, "network": "arbitrum"}, {"address": "0xdc69bec207725aea7308da7db0f17c8721687dcc", "name": "DEXFIN", "symbol": "DEXFIN", "decimals": null, "network": "arbitrum"}, {"address": "0x4026affabd9032bcc87fa05c02f088905f3dc09b", "name": "StakeWise", "symbol": "SWISE", "decimals": null, "network": "arbitrum"}, {"address": "0x4bdc50829003cc017443bf9bfb3ac82f3f0c4ad4", "name": "CLP Coin", "symbol": "CLPC", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Network Spirituality Agency", "symbol": "NSA", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "WINR Pod", "symbol": "apWINR", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Yakuza Finance", "symbol": "YAFI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ARB-ETH-C", "symbol": "ARB-ETH-C", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ARB-ETH-10x", "symbol": "ARB-ETH-10x", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Ultra Crypto Volatility Token", "symbol": "UCVI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "bitcoin2", "symbol": "BTC2", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ether.fi governance token", "symbol": "ETHFI", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "<PERSON>", "symbol": "BUCK", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "MOODENG", "symbol": "MOODENG", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "New Crypto Space", "symbol": "CRYPTO", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Uqinzen", "symbol": "UQn", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "ARC MV Scorpio Ⅰ", "symbol": "SCORPIO Ⅰ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BTBFinance", "symbol": "BTB", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "BUZcoin", "symbol": "BUZ", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Poison.Finance Pound Potion", "symbol": "pGBP", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Beluga", "symbol": "Beluga", "decimals": null, "network": "arbitrum"}, {"address": "******************************************", "name": "Contango Perpetual Option", "symbol": "oTANGO", "decimals": null, "network": "arbitrum"}, {"address": "0x606c3e5075e5555e79aa15f1e9facb776f96c248", "name": "Eigen", "symbol": "EIGEN", "decimals": null, "network": "arbitrum"}, {"address": "0x753e9b833245dc7f05841bcb060a1e5008114b42", "name": "Truesense AI", "symbol": "TRSS", "decimals": null, "network": "arbitrum"}, {"address": "0x0f13fc8a93ab8edc9fde0a1e19aac693161599a5", "name": "SecureChain AI", "symbol": "SCAI", "decimals": null, "network": "arbitrum"}, {"address": "0xd49512ce0bef6a15092d37779e8f654a99f0f5c9", "name": "AgentOllie.AI", "symbol": "OLLIE", "decimals": null, "network": "arbitrum"}, {"address": "0x52dbc179bd0c1a91d677851e2a9f51a53eff82d8", "name": "LFG Club", "symbol": "LFG", "decimals": null, "network": "arbitrum"}, {"address": "0x5e3660bba7e003f8af0831812bf68f436e11ff98", "name": "DNET", "symbol": "DNET", "decimals": null, "network": "arbitrum"}, {"address": "0x73189a3db11e63805a7696f8316f32a012bc735f", "name": "Web3 AI", "symbol": "Web3 AI", "decimals": null, "network": "arbitrum"}, {"address": "0xd33997484791fe6adb220df49d454a01efddc7ab", "name": "<PERSON>", "symbol": "ZARD", "decimals": null, "network": "arbitrum"}, {"address": "0x03e92235e5a567458ca58244fdc3851c6fbd1ab2", "name": "USDT.A", "symbol": "USDT.A", "decimals": null, "network": "arbitrum"}]