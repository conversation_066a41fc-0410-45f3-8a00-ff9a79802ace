/// <reference types="bn.js" />
/// <reference types="node" />
import { EventEmitter } from 'events';
import { BN, BNLike } from 'ethereumjs-util';
import { BootstrapNode, Chain as <PERSON><PERSON>hain, GenesisBlock, GenesisState, Hardfork as HardforkParams } from './types';
export declare enum CustomChain {
    /**
     * Polygon (Matic) Mainnet
     *
     * - [Documentation](https://docs.matic.network/docs/develop/network-details/network)
     */
    PolygonMainnet = "polygon-mainnet",
    /**
     * Polygon (Matic) Mumbai Testnet
     *
     * - [Documentation](https://docs.matic.network/docs/develop/network-details/network)
     */
    PolygonMumbai = "polygon-mumbai",
    /**
     * Arbitrum Rinkeby Testnet
     *
     * - [Documentation](https://developer.offchainlabs.com/docs/public_testnet)
     */
    ArbitrumRinkebyTestnet = "arbitrum-rinkeby-testnet",
    /**
     * xDai EVM sidechain with a native stable token
     *
     * - [Documentation](https://www.xdaichain.com/)
     */
    xDaiChain = "x-dai-chain",
    /**
     * Optimistic Kovan - testnet for Optimism roll-up
     *
     * - [Documentation](https://community.optimism.io/docs/developers/tutorials.html)
     */
    OptimisticKovan = "optimistic-kovan",
    /**
     * Optimistic Ethereum - mainnet for Optimism roll-up
     *
     * - [Documentation](https://community.optimism.io/docs/developers/tutorials.html)
     */
    OptimisticEthereum = "optimistic-ethereum"
}
export declare enum Chain {
    Mainnet = 1,
    Ropsten = 3,
    Rinkeby = 4,
    Kovan = 42,
    Goerli = 5,
    Sepolia = 11155111
}
export declare enum Hardfork {
    Chainstart = "chainstart",
    Homestead = "homestead",
    Dao = "dao",
    TangerineWhistle = "tangerineWhistle",
    SpuriousDragon = "spuriousDragon",
    Byzantium = "byzantium",
    Constantinople = "constantinople",
    Petersburg = "petersburg",
    Istanbul = "istanbul",
    MuirGlacier = "muirGlacier",
    Berlin = "berlin",
    London = "london",
    ArrowGlacier = "arrowGlacier",
    GrayGlacier = "grayGlacier",
    MergeForkIdTransition = "mergeForkIdTransition",
    Merge = "merge",
    Shanghai = "shanghai"
}
export declare enum ConsensusType {
    ProofOfStake = "pos",
    ProofOfWork = "pow",
    ProofOfAuthority = "poa"
}
export declare enum ConsensusAlgorithm {
    Ethash = "ethash",
    Clique = "clique",
    Casper = "casper"
}
interface BaseOpts {
    /**
     * String identifier ('byzantium') for hardfork or {@link Hardfork} enum.
     *
     * Default: Hardfork.Istanbul
     */
    hardfork?: string | Hardfork;
    /**
     * Limit parameter returns to the given hardforks
     */
    supportedHardforks?: Array<string | Hardfork>;
    /**
     * Selected EIPs which can be activated, please use an array for instantiation
     * (e.g. `eips: [ 2537, ]`)
     *
     * Currently supported:
     *
     * - [EIP-2537](https://eips.ethereum.org/EIPS/eip-2537) - BLS12-381 precompiles
     */
    eips?: number[];
}
/**
 * Options for instantiating a {@link Common} instance.
 */
export interface CommonOpts extends BaseOpts {
    /**
     * Chain name ('mainnet'), id (1), or {@link Chain} enum,
     * either from a chain directly supported or a custom chain
     * passed in via {@link CommonOpts.customChains}.
     */
    chain: string | number | Chain | BN | object;
    /**
     * Initialize (in addition to the supported chains) with the selected
     * custom chains
     *
     * Usage (directly with the respective chain intialization via the {@link CommonOpts.chain} option):
     *
     * Pattern 1 (without genesis state):
     *
     * ```javascript
     * import myCustomChain1 from '[PATH_TO_MY_CHAINS]/myCustomChain1.json'
     * const common = new Common({ chain: 'myCustomChain1', customChains: [ myCustomChain1 ]})
     * ```
     *
     * Pattern 2 (with genesis state see {@link GenesisState} for format):
     *
     * ```javascript
     * const simpleState = {
     *   '0x0...01': '0x100', // For EoA
     * }
     * import myCustomChain1 from '[PATH_TO_MY_CHAINS]/myCustomChain1.json'
     * import chain1GenesisState from '[PATH_TO_GENESIS_STATES]/chain1GenesisState.json'
     * const common = new Common({ chain: 'myCustomChain1', customChains: [ [ myCustomChain1, simpleState ] ]})
     * ```
     *
     * Pattern 3 (with complex genesis state, containing contract accounts and storage).
     * Note that in {@link AccountState} there are two
     * accepted types. This allows to easily insert accounts in the genesis state:
     *
     * A complex genesis state with Contract and EoA states would have the following format:
     *
     * ```javascript
     * const complexState = {
     *   '0x0...01': '0x100', // For EoA
     *   '0x0...02': ['0x1', '0xRUNTIME_BYTECODE', [[ keyOne, valueOne ], [ keyTwo, valueTwo ]]] // For contracts
     * }
     * import myCustomChain1 from '[PATH_TO_MY_CHAINS]/myCustomChain1.json'
     * const common = new Common({ chain: 'myCustomChain1', customChains: [ [ myCustomChain1, complexState ] ]})
     * ```
     */
    customChains?: IChain[] | [IChain, GenesisState][];
}
/**
 * Options to be used with the {@link Common.custom} static constructor.
 */
export interface CustomCommonOpts extends BaseOpts {
    /**
     * The name (`mainnet`), id (`1`), or {@link Chain} enum of
     * a standard chain used to base the custom chain params on.
     */
    baseChain?: string | number | Chain | BN;
}
interface hardforkOptions {
    /** optional, only allow supported HFs (default: false) */
    onlySupported?: boolean;
    /** optional, only active HFs (default: false) */
    onlyActive?: boolean;
}
/**
 * Common class to access chain and hardfork parameters and to provide
 * a unified and shared view on the network and hardfork state.
 *
 * Use the {@link Common.custom} static constructor for creating simple
 * custom chain {@link Common} objects (more complete custom chain setups
 * can be created via the main constructor and the {@link CommonOpts.customChains} parameter).
 */
export default class Common extends EventEmitter {
    readonly DEFAULT_HARDFORK: string | Hardfork;
    private _chainParams;
    private _hardfork;
    private _supportedHardforks;
    private _eips;
    private _customChains;
    /**
     * Creates a {@link Common} object for a custom chain, based on a standard one.
     *
     * It uses all the {@link Chain} parameters from the {@link baseChain} option except the ones overridden
     * in a provided {@link chainParamsOrName} dictionary. Some usage example:
     *
     * ```javascript
     * Common.custom({chainId: 123})
     * ```
     *
     * There are also selected supported custom chains which can be initialized by using one of the
     * {@link CustomChains} for {@link chainParamsOrName}, e.g.:
     *
     * ```javascript
     * Common.custom(CustomChains.MaticMumbai)
     * ```
     *
     * Note that these supported custom chains only provide some base parameters (usually the chain and
     * network ID and a name) and can only be used for selected use cases (e.g. sending a tx with
     * the `@ethereumjs/tx` library to a Layer-2 chain).
     *
     * @param chainParamsOrName Custom parameter dict (`name` will default to `custom-chain`) or string with name of a supported custom chain
     * @param opts Custom chain options to set the {@link CustomCommonOpts.baseChain}, selected {@link CustomCommonOpts.hardfork} and others
     */
    static custom(chainParamsOrName: Partial<IChain> | CustomChain, opts?: CustomCommonOpts): Common;
    /**
     * Creates a {@link Common} object for a custom chain, based on a standard one. It uses all the `Chain`
     * params from {@link baseChain} except the ones overridden in {@link customChainParams}.
     *
     * @deprecated Use {@link Common.custom} instead
     *
     * @param baseChain The name (`mainnet`) or id (`1`) of a standard chain used to base the custom
     * chain params on.
     * @param customChainParams The custom parameters of the chain.
     * @param hardfork String identifier ('byzantium') for hardfork (optional)
     * @param supportedHardforks Limit parameter returns to the given hardforks (optional)
     */
    static forCustomChain(baseChain: string | number | Chain, customChainParams: Partial<IChain>, hardfork?: string | Hardfork, supportedHardforks?: Array<string | Hardfork>): Common;
    /**
     * Static method to determine if a {@link chainId} is supported as a standard chain
     * @param chainId BN id (`1`) of a standard chain
     * @returns boolean
     */
    static isSupportedChainId(chainId: BN): boolean;
    private static _getChainParams;
    /**
     *
     * @constructor
     */
    constructor(opts: CommonOpts);
    /**
     * Sets the chain
     * @param chain String ('mainnet') or Number (1) chain
     *     representation. Or, a Dictionary of chain parameters for a private network.
     * @returns The dictionary with parameters set as chain
     */
    setChain(chain: string | number | Chain | BN | object): any;
    /**
     * Sets the hardfork to get params for
     * @param hardfork String identifier (e.g. 'byzantium') or {@link Hardfork} enum
     */
    setHardfork(hardfork: string | Hardfork): void;
    /**
     * Returns the hardfork based on the block number or an optional
     * total difficulty (Merge HF) provided.
     *
     * An optional TD takes precedence in case the corresponding HF block
     * is set to `null` or otherwise needs to match (if not an error
     * will be thrown).
     *
     * @param blockNumber
     * @param td
     * @returns The name of the HF
     */
    getHardforkByBlockNumber(blockNumber: BNLike, td?: BNLike): string;
    /**
     * Sets a new hardfork based on the block number or an optional
     * total difficulty (Merge HF) provided.
     *
     * An optional TD takes precedence in case the corresponding HF block
     * is set to `null` or otherwise needs to match (if not an error
     * will be thrown).
     *
     * @param blockNumber
     * @param td
     * @returns The name of the HF set
     */
    setHardforkByBlockNumber(blockNumber: BNLike, td?: BNLike): string;
    /**
     * Internal helper function to choose between hardfork set and hardfork provided as param
     * @param hardfork Hardfork given to function as a parameter
     * @returns Hardfork chosen to be used
     */
    _chooseHardfork(hardfork?: string | Hardfork | null, onlySupported?: boolean): string;
    /**
     * Internal helper function, returns the params for the given hardfork for the chain set
     * @param hardfork Hardfork name
     * @returns Dictionary with hardfork params
     */
    _getHardfork(hardfork: string | Hardfork): any;
    /**
     * Internal helper function to check if a hardfork is set to be supported by the library
     * @param hardfork Hardfork name
     * @returns True if hardfork is supported
     */
    _isSupportedHardfork(hardfork: string | Hardfork | null): boolean;
    /**
     * Sets the active EIPs
     * @param eips
     */
    setEIPs(eips?: number[]): void;
    /**
     * Returns a parameter for the current chain setup
     *
     * If the parameter is present in an EIP, the EIP always takes precendence.
     * Otherwise the parameter if taken from the latest applied HF with
     * a change on the respective parameter.
     *
     * @param topic Parameter topic ('gasConfig', 'gasPrices', 'vm', 'pow')
     * @param name Parameter name (e.g. 'minGasLimit' for 'gasConfig' topic)
     * @returns The value requested or `null` if not found
     */
    param(topic: string, name: string): any;
    /**
     * Returns the parameter corresponding to a hardfork
     * @param topic Parameter topic ('gasConfig', 'gasPrices', 'vm', 'pow')
     * @param name Parameter name (e.g. 'minGasLimit' for 'gasConfig' topic)
     * @param hardfork Hardfork name
     * @returns The value requested or `null` if not found
     */
    paramByHardfork(topic: string, name: string, hardfork: string | Hardfork): any;
    /**
     * Returns a parameter corresponding to an EIP
     * @param topic Parameter topic ('gasConfig', 'gasPrices', 'vm', 'pow')
     * @param name Parameter name (e.g. 'minGasLimit' for 'gasConfig' topic)
     * @param eip Number of the EIP
     * @returns The value requested or `null` if not found
     */
    paramByEIP(topic: string, name: string, eip: number): any;
    /**
     * Returns a parameter for the hardfork active on block number
     * @param topic Parameter topic
     * @param name Parameter name
     * @param blockNumber Block number
     */
    paramByBlock(topic: string, name: string, blockNumber: BNLike): any;
    /**
     * Checks if an EIP is activated by either being included in the EIPs
     * manually passed in with the {@link CommonOpts.eips} or in a
     * hardfork currently being active
     *
     * Note: this method only works for EIPs being supported
     * by the {@link CommonOpts.eips} constructor option
     * @param eip
     */
    isActivatedEIP(eip: number): boolean;
    /**
     * Checks if set or provided hardfork is active on block number
     * @param hardfork Hardfork name or null (for HF set)
     * @param blockNumber
     * @param opts Hardfork options (onlyActive unused)
     * @returns True if HF is active on block number
     */
    hardforkIsActiveOnBlock(hardfork: string | Hardfork | null, blockNumber: BNLike, opts?: hardforkOptions): boolean;
    /**
     * Alias to hardforkIsActiveOnBlock when hardfork is set
     * @param blockNumber
     * @param opts Hardfork options (onlyActive unused)
     * @returns True if HF is active on block number
     */
    activeOnBlock(blockNumber: BNLike, opts?: hardforkOptions): boolean;
    /**
     * Sequence based check if given or set HF1 is greater than or equal HF2
     * @param hardfork1 Hardfork name or null (if set)
     * @param hardfork2 Hardfork name
     * @param opts Hardfork options
     * @returns True if HF1 gte HF2
     */
    hardforkGteHardfork(hardfork1: string | Hardfork | null, hardfork2: string | Hardfork, opts?: hardforkOptions): boolean;
    /**
     * Alias to hardforkGteHardfork when hardfork is set
     * @param hardfork Hardfork name
     * @param opts Hardfork options
     * @returns True if hardfork set is greater than hardfork provided
     */
    gteHardfork(hardfork: string | Hardfork, opts?: hardforkOptions): boolean;
    /**
     * Checks if given or set hardfork is active on the chain
     * @param hardfork Hardfork name, optional if HF set
     * @param opts Hardfork options (onlyActive unused)
     * @returns True if hardfork is active on the chain
     */
    hardforkIsActiveOnChain(hardfork?: string | Hardfork | null, opts?: hardforkOptions): boolean;
    /**
     * Returns the active hardfork switches for the current chain
     * @param blockNumber up to block if provided, otherwise for the whole chain
     * @param opts Hardfork options (onlyActive unused)
     * @return Array with hardfork arrays
     */
    activeHardforks(blockNumber?: BNLike | null, opts?: hardforkOptions): HardforkParams[];
    /**
     * Returns the latest active hardfork name for chain or block or throws if unavailable
     * @param blockNumber up to block if provided, otherwise for the whole chain
     * @param opts Hardfork options (onlyActive unused)
     * @return Hardfork name
     */
    activeHardfork(blockNumber?: BNLike | null, opts?: hardforkOptions): string;
    /**
     * Returns the hardfork change block for hardfork provided or set
     * @param hardfork Hardfork name, optional if HF set
     * @returns Block number or null if unscheduled
     * @deprecated Please use {@link Common.hardforkBlockBN} for large number support
     */
    hardforkBlock(hardfork?: string | Hardfork): number | null;
    /**
     * Returns the hardfork change block for hardfork provided or set
     * @param hardfork Hardfork name, optional if HF set
     * @returns Block number or null if unscheduled
     */
    hardforkBlockBN(hardfork?: string | Hardfork): BN | null;
    /**
     * Returns the hardfork change total difficulty (Merge HF) for hardfork provided or set
     * @param hardfork Hardfork name, optional if HF set
     * @returns Total difficulty or null if no set
     */
    hardforkTD(hardfork?: string | Hardfork): BN | null;
    /**
     * True if block number provided is the hardfork (given or set) change block
     * @param blockNumber Number of the block to check
     * @param hardfork Hardfork name, optional if HF set
     * @returns True if blockNumber is HF block
     */
    isHardforkBlock(blockNumber: BNLike, hardfork?: string | Hardfork): boolean;
    /**
     * Returns the change block for the next hardfork after the hardfork provided or set
     * @param hardfork Hardfork name, optional if HF set
     * @returns Block number or null if not available
     * @deprecated Please use {@link Common.nextHardforkBlockBN} for large number support
     */
    nextHardforkBlock(hardfork?: string | Hardfork): number | null;
    /**
     * Returns the change block for the next hardfork after the hardfork provided or set
     * @param hardfork Hardfork name, optional if HF set
     * @returns Block number or null if not available
     */
    nextHardforkBlockBN(hardfork?: string | Hardfork): BN | null;
    /**
     * True if block number provided is the hardfork change block following the hardfork given or set
     * @param blockNumber Number of the block to check
     * @param hardfork Hardfork name, optional if HF set
     * @returns True if blockNumber is HF block
     */
    isNextHardforkBlock(blockNumber: BNLike, hardfork?: string | Hardfork): boolean;
    /**
     * Internal helper function to calculate a fork hash
     * @param hardfork Hardfork name
     * @returns Fork hash as hex string
     */
    _calcForkHash(hardfork: string | Hardfork): string;
    /**
     * Returns an eth/64 compliant fork hash (EIP-2124)
     * @param hardfork Hardfork name, optional if HF set
     */
    forkHash(hardfork?: string | Hardfork): any;
    /**
     *
     * @param forkHash Fork hash as a hex string
     * @returns Array with hardfork data (name, block, forkHash)
     */
    hardforkForForkHash(forkHash: string): any | null;
    /**
     * Returns the Genesis parameters of the current chain
     * @returns Genesis dictionary
     */
    genesis(): GenesisBlock;
    /**
     * Returns the Genesis state of the current chain,
     * all values are provided as hex-prefixed strings.
     */
    genesisState(): GenesisState;
    /**
     * Returns the hardforks for current chain
     * @returns {Array} Array with arrays of hardforks
     */
    hardforks(): HardforkParams[];
    /**
     * Returns bootstrap nodes for the current chain
     * @returns {Dictionary} Dict with bootstrap nodes
     */
    bootstrapNodes(): BootstrapNode[];
    /**
     * Returns DNS networks for the current chain
     * @returns {String[]} Array of DNS ENR urls
     */
    dnsNetworks(): string[];
    /**
     * Returns the hardfork set
     * @returns Hardfork name
     */
    hardfork(): string | Hardfork;
    /**
     * Returns the Id of current chain
     * @returns chain Id
     * @deprecated Please use {@link Common.chainIdBN} for large number support
     */
    chainId(): number;
    /**
     * Returns the Id of current chain
     * @returns chain Id
     */
    chainIdBN(): BN;
    /**
     * Returns the name of current chain
     * @returns chain name (lower case)
     */
    chainName(): string;
    /**
     * Returns the Id of current network
     * @returns network Id
     * @deprecated Please use {@link Common.networkIdBN} for large number support
     */
    networkId(): number;
    /**
     * Returns the Id of current network
     * @returns network Id
     */
    networkIdBN(): BN;
    /**
     * Returns the active EIPs
     * @returns List of EIPs
     */
    eips(): number[];
    /**
     * Returns the consensus type of the network
     * Possible values: "pow"|"poa"|"pos"
     *
     * Note: This value can update along a hardfork.
     */
    consensusType(): string | ConsensusType;
    /**
     * Returns the concrete consensus implementation
     * algorithm or protocol for the network
     * e.g. "ethash" for "pow" consensus type,
     * "clique" for "poa" consensus type or
     * "casper" for "pos" consensus type.
     *
     * Note: This value can update along a hardfork.
     */
    consensusAlgorithm(): string | ConsensusAlgorithm;
    /**
     * Returns a dictionary with consensus configuration
     * parameters based on the consensus algorithm
     *
     * Expected returns (parameters must be present in
     * the respective chain json files):
     *
     * ethash: -
     * clique: period, epoch
     * aura: -
     * casper: -
     *
     * Note: This value can update along a hardfork.
     */
    consensusConfig(): {
        [key: string]: any;
    };
    /**
     * Returns a deep copy of this {@link Common} instance.
     */
    copy(): Common;
}
export {};
