// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`all USDT are countervalue enabled 1`] = `
[
  "algorand/asa/312769",
  "bsc/bep20/binance-peg_bsc-usd",
  "bsc/bep20/tether_usd_wormhole_0x524bc91dc82d6b90ef29f76a3ecaabafffd490bc",
  "ethereum/erc20/usd_tether__erc20_",
  "multiversx/esdt/555344542d663863303863",
  "polygon/erc20/(pos)_tether_usd",
  "solana/spl/es9vmfrzacermjfrf4h2fyd4kconky11mcce8benwnyb",
  "ton/jetton/eqcxe6mutqjkfngfarotkot1lzbdiix1kcixrv7nw2id_sds",
  "tron/trc20/tr7nhqjekqxgtci8q8zy4pl8otszgjlj6t",
]
`;
