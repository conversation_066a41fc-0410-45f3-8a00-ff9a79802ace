@echo off
setlocal enabledelayedexpansion

set "DEFAULT_CONFIG=..\..\..\config\rpc.yaml"
set "CCIP_SCRIPT=.\src\index.ts"

if "%~1"=="" (
    echo CCIP Tools - Windows Helper
    echo ==========================
    echo.
    echo Usage: ccip-simple.bat ^<command^> [options]
    echo.
    echo Commands:
    echo   list-networks
    echo   getSupportedTokens ^<network^>
    echo   send ^<source^> ^<router^> ^<dest^> --receiver ^<address^>
    echo   show ^<tx_hash^>
    echo   manualExec ^<tx_hash^>
    echo.
    echo Examples:
    echo   ccip-simple.bat list-networks
    echo   ccip-simple.bat getSupportedTokens ethereum
    echo   ccip-simple.bat send ethereum 0xRouter polygon --receiver 0xAddress
    echo.
    goto :eof
)

set "ARGS="
:parse_args
if "%~1"=="" goto :execute_command
set "ARGS=!ARGS! %~1"
shift
goto :parse_args

:execute_command
echo %ARGS% | findstr /i "yaml-config" >nul
if %errorlevel% neq 0 (
    set "ARGS=!ARGS! --yaml-config "%DEFAULT_CONFIG%""
)

echo Executing: npx tsx "%CCIP_SCRIPT%" !ARGS!
npx tsx "%CCIP_SCRIPT%" !ARGS!

endlocal
