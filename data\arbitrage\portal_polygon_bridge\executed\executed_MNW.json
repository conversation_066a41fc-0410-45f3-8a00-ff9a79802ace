[{"opportunity_hash": "MNW_223.9_2025-06-05 11:33:36", "symbol": "MNW", "chain": "ethereum", "buy_chain": "ethereum", "eth_address": "******************************************", "polygon_address": "", "time": "2025-06-05 11:33:38", "usdt_input": 223.9, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 223.9 USDT，可用: 192.435834 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_MNW.log"}, {"opportunity_hash": "MNW_234.28_2025-06-09 09:36:55", "symbol": "MNW", "chain": "polygon", "buy_chain": "polygon", "eth_address": "", "polygon_address": "******************************************", "time": "2025-06-09 09:36:57", "usdt_input": 234.28, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 234.28 USDT，可用: 222.866555 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_MNW.log"}, {"opportunity_hash": "MNW_256.18_2025-06-10 03:02:53", "symbol": "MNW", "chain": "polygon", "buy_chain": "polygon", "eth_address": "", "polygon_address": "******************************************", "time": "2025-06-10 03:02:55", "usdt_input": 256.18, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 256.18 USDT，可用: 35.201236 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_MNW.log"}, {"opportunity_hash": "MNW_280.98_2025-06-10 04:58:50", "symbol": "MNW", "chain": "polygon", "buy_chain": "polygon", "eth_address": "", "polygon_address": "******************************************", "time": "2025-06-10 04:58:52", "usdt_input": 280.98, "success": false, "amount_bought": 0, "tx_hash": "", "price": 0, "error": "交易失败: 代币余额不足。请求: 280.98 USDT，可用: 35.201236 USDT", "log_file": "C:\\Users\\<USER>\\CascadeProjects\\cex_dex_arb_dev\\scripts\\arbitrage\\portal_polygon_bridge\\results\\trade\\executor_MNW.log"}]