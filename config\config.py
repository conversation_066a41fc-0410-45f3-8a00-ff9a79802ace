#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置加载模块，用于从YAML文件加载配置
"""

import os
import yaml
from typing import Dict, Any, Optional


class Config:
    """配置管理类，负责加载和访问配置"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理对象
        
        Args:
            config_path: 配置文件路径，默认为当前目录下的config.yaml
        """
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, 'config.yaml')
        
        self.config_path = config_path
        self.config_data = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        从YAML文件加载配置
        
        Returns:
            配置数据字典
        
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"配置文件格式错误: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取指定键的配置值
        
        Args:
            key: 配置键，支持点号分隔的路径，如"cex.gate.api_key"
            default: 默认值，当键不存在时返回
        
        Returns:
            配置值或默认值
        """
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_cex_config(self, exchange: str) -> Optional[Dict[str, Any]]:
        """
        获取指定中心化交易所的配置
        
        Args:
            exchange: 交易所名称，如"gate"
        
        Returns:
            交易所配置字典或None
        """
        return self.get(f"cex.{exchange}")
    
    def get_dex_config(self, network: str) -> Optional[Dict[str, Any]]:
        """
        获取指定去中心化交易所(网络)的配置
        
        Args:
            network: 网络名称，如"astar"
        
        Returns:
            DEX配置字典或None
        """
        return self.get(f"dex.{network}")
    
    def get_arbitrage_config(self) -> Optional[Dict[str, Any]]:
        """
        获取套利策略配置
        
        Returns:
            套利配置字典或None
        """
        return self.get("arbitrage")
    
    def get_logging_config(self) -> Optional[Dict[str, Any]]:
        """
        获取日志配置
        
        Returns:
            日志配置字典或None
        """
        return self.get("logging")


# 创建全局配置对象
config = Config()


if __name__ == "__main__":
    # 测试配置加载
    try:
        print("加载配置...")
        gate_config = config.get_cex_config("gate")
        print(f"Gate API Key: {gate_config.get('api_key')}")
        
        astar_config = config.get_dex_config("astar")
        print(f"Astar RPC: {astar_config.get('rpc', {}).get('endpoint')}")
        
        log_config = config.get_logging_config()
        print(f"日志级别: {log_config.get('level')}")
        
        print("配置加载成功!")
    except Exception as e:
        print(f"配置加载失败: {e}") 