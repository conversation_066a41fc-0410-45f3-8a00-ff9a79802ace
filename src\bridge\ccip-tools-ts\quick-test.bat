@echo off
echo 🧪 Quick Test of CCIP Network Names
echo ====================================

echo.
echo Testing list-networks command...
npx tsx src\index.ts list-networks

echo.
echo Testing network name validation...
echo Testing "ethereum" network:
npx tsx src\index.ts getSupportedTokens ethereum 2>nul
if %errorlevel% equ 0 (
    echo ✅ "ethereum" network name works
) else (
    echo ❌ "ethereum" network name failed
)

echo.
echo Testing "polygon" network:
npx tsx src\index.ts getSupportedTokens polygon 2>nul
if %errorlevel% equ 0 (
    echo ✅ "polygon" network name works
) else (
    echo ❌ "polygon" network name failed
)

echo.
echo Testing "sepolia" network:
npx tsx src\index.ts getSupportedTokens sepolia 2>nul
if %errorlevel% equ 0 (
    echo ✅ "sepolia" network name works
) else (
    echo ❌ "sepolia" network name failed
)

echo.
echo Testing invalid network:
npx tsx src\index.ts getSupportedTokens invalid-network 2>nul
if %errorlevel% neq 0 (
    echo ✅ Invalid network properly rejected
) else (
    echo ❌ Invalid network was accepted
)

echo.
echo ✨ Quick test completed!
