#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Polygon EIP-1559修复
验证POA链基础费用获取和交易参数构建是否正确
"""

import asyncio
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__)))

from src.dex.KyberSwap.client import KyberSwapClient

async def test_polygon_base_fee():
    """测试Polygon基础费用获取"""
    print("=" * 60)
    print("测试Polygon基础费用获取修复")
    print("=" * 60)
    
    try:
        # 创建Polygon客户端
        client = KyberSwapClient(chain="polygon")
        
        print(f"🔍 测试链: {client.chain}")
        print(f"EIP-1559支持: {'✅ 是' if client._supports_eip1559() else '❌ 否'}")
        
        if client._supports_eip1559():
            print("\n📊 测试基础费用获取:")
            
            # 测试基础费用获取（应该不再出现POA错误）
            try:
                base_fee = client._get_base_fee()
                print(f"   ✅ 基础费用获取成功: {base_fee / 10**9:.2f} Gwei")
                
                # 验证基础费用是否合理（应该大于0）
                if base_fee > 0:
                    print(f"   ✅ 基础费用值合理")
                else:
                    print(f"   ⚠️ 基础费用为0，可能有问题")
                    
            except Exception as e:
                print(f"   ❌ 基础费用获取失败: {str(e)}")
                return False
            
            # 测试优先费用
            try:
                priority_fee = client._calculate_priority_fee()
                print(f"   ✅ 优先费用获取成功: {priority_fee / 10**9:.2f} Gwei")
                
                # 验证Polygon优先费用是否为30 Gwei
                expected_priority_fee = client.web3.to_wei(30, 'gwei')
                if priority_fee == expected_priority_fee:
                    print(f"   ✅ Polygon优先费用设置正确 (30 Gwei)")
                else:
                    print(f"   ⚠️ Polygon优先费用不正确，期望30 Gwei，实际{priority_fee / 10**9:.2f} Gwei")
                    
            except Exception as e:
                print(f"   ❌ 优先费用获取失败: {str(e)}")
                return False
            
            # 计算总费用
            max_fee_per_gas = base_fee + priority_fee
            print(f"   📊 最大费用: {max_fee_per_gas / 10**9:.2f} Gwei")
            
            return True
        else:
            print("❌ Polygon应该支持EIP-1559")
            return False
            
    except Exception as e:
        print(f"❌ 测试Polygon基础费用时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_params_building():
    """测试交易参数构建（不需要网络连接）"""
    print("\n" + "=" * 60)
    print("测试交易参数构建逻辑")
    print("=" * 60)
    
    try:
        from src.dex.KyberSwap.client import KyberSwapClient
        
        # 创建模拟客户端
        client = object.__new__(KyberSwapClient)
        client.chain = "polygon"
        
        # 模拟web3对象
        class MockWeb3:
            @staticmethod
            def to_wei(amount, unit):
                if unit == 'gwei':
                    return int(amount * 10**9)
                return amount
            
            class eth:
                @staticmethod
                def gas_price():
                    return int(20 * 10**9)  # 20 Gwei
        
        client.web3 = MockWeb3()
        
        print("🔍 测试EIP-1559参数构建:")
        
        # 测试EIP-1559支持
        supports_eip1559 = client._supports_eip1559()
        print(f"   EIP-1559支持: {'✅ 是' if supports_eip1559 else '❌ 否'}")
        
        if supports_eip1559:
            # 测试基础费用计算（使用gas价格估算）
            base_fee = client._get_base_fee()
            expected_base_fee = int(20 * 10**9 * 0.7)  # 20 Gwei * 0.7
            
            print(f"   基础费用: {base_fee / 10**9:.2f} Gwei")
            print(f"   期望值: {expected_base_fee / 10**9:.2f} Gwei")
            
            if base_fee == expected_base_fee:
                print("   ✅ Polygon基础费用计算正确")
            else:
                print("   ⚠️ Polygon基础费用计算可能有问题")
            
            # 测试优先费用
            priority_fee = client._calculate_priority_fee()
            expected_priority_fee = int(30 * 10**9)  # 30 Gwei
            
            print(f"   优先费用: {priority_fee / 10**9:.2f} Gwei")
            print(f"   期望值: {expected_priority_fee / 10**9:.2f} Gwei")
            
            if priority_fee == expected_priority_fee:
                print("   ✅ Polygon优先费用计算正确")
            else:
                print("   ⚠️ Polygon优先费用计算可能有问题")
            
            # 模拟EIP-1559交易参数构建
            tx_params = {
                'from': '0x1234567890123456789012345678901234567890',
                'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5',
                'value': 0,
                'data': '0x',
                'gas': 200000,
                'nonce': 1
            }
            
            # 添加EIP-1559参数
            max_fee_per_gas = base_fee + priority_fee
            tx_params.update({
                'type': 2,
                'maxFeePerGas': max_fee_per_gas,
                'maxPriorityFeePerGas': priority_fee
            })
            
            print(f"\n📋 模拟的EIP-1559交易参数:")
            print(f"   type: {tx_params.get('type')}")
            print(f"   maxFeePerGas: {tx_params.get('maxFeePerGas')} wei ({tx_params.get('maxFeePerGas', 0) / 10**9:.2f} Gwei)")
            print(f"   maxPriorityFeePerGas: {tx_params.get('maxPriorityFeePerGas')} wei ({tx_params.get('maxPriorityFeePerGas', 0) / 10**9:.2f} Gwei)")
            
            # 检查是否包含gasPrice（不应该包含）
            if 'gasPrice' in tx_params:
                print("   ❌ 错误：EIP-1559交易不应该包含gasPrice参数")
                return False
            else:
                print("   ✅ 正确：EIP-1559交易不包含gasPrice参数")
            
            # 模拟eth_call参数构建
            call_params = {
                "from": tx_params["from"],
                "to": tx_params["to"],
                "value": tx_params["value"],
                "data": tx_params["data"],
                "gas": tx_params["gas"]
            }
            
            print(f"\n📋 模拟的eth_call参数:")
            for key, value in call_params.items():
                print(f"   {key}: {value}")
            
            # 检查eth_call参数是否包含gas费用参数（不应该包含）
            gas_fee_params = ['gasPrice', 'maxFeePerGas', 'maxPriorityFeePerGas']
            has_gas_fee_params = any(param in call_params for param in gas_fee_params)
            
            if has_gas_fee_params:
                print("   ❌ 错误：eth_call参数不应该包含gas费用参数")
                return False
            else:
                print("   ✅ 正确：eth_call参数不包含gas费用参数")
            
            return True
        else:
            print("❌ Polygon应该支持EIP-1559")
            return False
            
    except Exception as e:
        print(f"❌ 测试交易参数构建时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_fixes():
    """显示修复内容"""
    print("\n" + "=" * 60)
    print("修复内容总结")
    print("=" * 60)
    
    print("🔧 修复1: Polygon POA链基础费用获取")
    print("   问题: 'extraData is 271 bytes' POA链错误")
    print("   修复: 针对Polygon直接使用gas价格估算")
    print("   代码: base_fee = int(current_gas_price * 0.7)")
    
    print("\n🔧 修复2: EIP-1559交易预验证参数冲突")
    print("   问题: eth_call中包含gasPrice参数导致'gasPrice'错误")
    print("   修复: eth_call只包含必要参数，不包含gas费用参数")
    print("   代码: call_params不包含gasPrice/maxFeePerGas等")
    
    print("\n📍 修改的文件位置:")
    print("   - src/dex/KyberSwap/client.py")
    print("   - _get_base_fee() 方法 (第2332-2374行)")
    print("   - _execute_swap() 方法中的预验证部分 (第1209-1222行)")
    
    print("\n✅ 预期效果:")
    print("   - 不再出现POA链错误")
    print("   - 不再出现'gasPrice'参数冲突")
    print("   - Polygon交易能正常执行")

async def main():
    """主函数"""
    print("🚀 开始Polygon EIP-1559修复测试")
    
    # 测试基础费用获取
    base_fee_ok = await test_polygon_base_fee()
    
    # 测试交易参数构建
    tx_params_ok = test_transaction_params_building()
    
    # 显示修复内容
    show_fixes()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    if base_fee_ok and tx_params_ok:
        print("✅ 所有测试通过！")
        print("✅ Polygon POA链基础费用获取已修复")
        print("✅ EIP-1559交易参数冲突已修复")
        print("✅ 现在可以正常在Polygon上执行交易")
    else:
        print("❌ 部分测试失败")
        if not base_fee_ok:
            print("❌ 基础费用获取仍有问题")
        if not tx_params_ok:
            print("❌ 交易参数构建仍有问题")
    
    print("\n✅ Polygon EIP-1559修复测试完成")

if __name__ == "__main__":
    asyncio.run(main())
