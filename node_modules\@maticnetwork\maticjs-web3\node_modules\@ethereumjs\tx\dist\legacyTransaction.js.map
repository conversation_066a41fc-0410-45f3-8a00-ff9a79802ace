{"version": 3, "file": "legacyTransaction.js", "sourceRoot": "", "sources": ["../src/legacyTransaction.ts"], "names": [], "mappings": ";;AAAA,qDAWwB;AACxB,mCAAuF;AACvF,uDAAmD;AAEnD,iCAA6C;AAE7C,MAAM,gBAAgB,GAAG,CAAC,CAAA;AAE1B;;GAEG;AACH,MAAqB,WAAY,SAAQ,iCAA4B;IA6EnE;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;;QACrD,KAAK,iCAAM,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC,CAAA;QAElD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAEpD,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,MAAM,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEjF,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,6BAAW,CAAC,EAAE;YACpD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAA;YACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QAEjE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;aAChE;iBAAM;gBACL,eAAe;gBACf,kFAAkF;gBAClF,sFAAsF;gBACtF,mGAAmG;gBACnG,oEAAoE;gBACpE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAE,CAAA;gBACjB,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAEtD,yCAAyC;gBACzC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;oBAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;iBAChE;aACF;SACF;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAA,2BAAoB,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACpD;QAED,MAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAvHD;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACrE,MAAM,MAAM,GAAG,qBAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QAErC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACxE,OAAO,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,uGAAuG;QACvG,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;SACF;QAED,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEpE,IAAA,yCAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,OAAO,IAAI,WAAW,CACpB;YACE,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,CAAC;YACD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAmDD;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACpE,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,SAAS;QACP,OAAO,qBAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC/B,CAAC;IAEO,iBAAiB;QACvB,MAAM,MAAM,GAAG;YACb,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;SACV,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,MAAM,CAAC,IAAI,CAAC,IAAA,0BAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;YAC9C,MAAM,CAAC,IAAI,CAAC,IAAA,6BAAW,EAAC,IAAA,0BAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACrC,MAAM,CAAC,IAAI,CAAC,IAAA,6BAAW,EAAC,IAAA,0BAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAmBD,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,IAAI,WAAW,EAAE;YACf,OAAO,IAAA,yBAAO,EAAC,OAAO,CAAC,CAAA;SACxB;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,KAAK,CAAC,UAAU,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;;;;OAKG;IACH,IAAI;QACF,+EAA+E;QAC/E,4DAA4D;QAC5D,0EAA0E;QAC1E,0EAA0E;QAC1E,EAAE;QACF,oFAAoF;QACpF,+CAA+C;QAC/C,EAAE;QACF,yEAAyE;QACzE,EAAE;QACF,yBAAyB;QACzB,sFAAsF;QACtF,wBAAwB;QACxB,GAAG;QAEH,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;aACtC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,IAAA,yBAAO,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,OAAO,IAAA,yBAAO,EAAC,OAAO,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,kBAAkB;;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAElD,uGAAuG;QACvG,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,IAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QACxB,IAAI;YACF,OAAO,IAAA,2BAAS,EACd,OAAO,EACP,CAAE,EACF,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CACvF,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACzD,MAAM,GAAG,GAAG,IAAI,oBAAE,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SAClD;QAED,MAAM,IAAI,mCAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAA;QAEvD,OAAO,WAAW,CAAC,UAAU,CAC3B;YACE,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;SACb,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACtD,CAAA;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,CAAM,EAAE,MAAe;QAC1C,8DAA8D;QAC9D,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACzC,MAAM,IAAI,KAAK,CACb,oFAAoF,CAAC,EAAE,CACxF,CAAA;aACF;SACF;QAED,IAAI,SAAS,CAAA;QACb,6DAA6D;QAC7D,IACE,CAAC,KAAK,SAAS;YACf,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACV,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EACV;YACA,IAAI,MAAM,EAAE;gBACV,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACjD,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;gBAErF,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CACb,+BAA+B,CAAC,iBAAiB,MAAM,CAAC,SAAS,EAAE,gFAAgF,CACpJ,CAAA;iBACF;aACF;iBAAM;gBACL,+BAA+B;gBAC/B,IAAI,MAAM,CAAA;gBACV,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,MAAM,GAAG,EAAE,CAAA;iBACZ;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,iDAAiD;gBACjD,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACnC;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;QAEtE,eAAe;QACf,4UAA4U;QAC5U,MAAM,CAAC,GAAG,IAAI,CAAC,CAAE,CAAA;QAEjB,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEtD,MAAM,+BAA+B,GACnC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QAEhE,OAAO,+BAA+B,IAAI,oBAAoB,CAAA;IAChE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAA;QACxC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;CACF;AAhcD,8BAgcC"}