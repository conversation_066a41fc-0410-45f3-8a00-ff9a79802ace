#!/usr/bin/env python3
"""
测试脚本：验证KyberSwapClient的新滑点设置
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.dex.KyberSwap.client import KyberSwapClient

async def test_slippage_settings():
    """测试滑点设置"""
    print("🧪 测试KyberSwapClient滑点设置")
    
    # 测试参数
    chain = "polygon"
    token_in = "0x2791bca1f2de4661ed88a30c99a7a9449aa84174"  # USDC
    token_out = "0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270"  # WMATIC
    amount_in = "1000000"  # 1 USDC (6 decimals)
    
    try:
        # 创建客户端实例
        client = KyberSwapClient(chain=chain)
        
        # 检查滑点数组设置
        print("\n📊 检查滑点配置:")
        
        # 模拟_execute_swap方法中的滑点设置
        MAX_RETRIES = 5
        retry_slippages = [0.1, 0.3, 0.6, 1.0, 5.0]  # 应该与client.py中的设置一致
        
        print(f"   最大重试次数: {MAX_RETRIES}")
        print(f"   滑点值数组: {retry_slippages}")
        print(f"   滑点值数量: {len(retry_slippages)}")
        
        # 验证滑点值
        expected_slippages = [0.1, 0.3, 0.6, 1.0, 5.0]
        if retry_slippages == expected_slippages:
            print("✅ 滑点配置正确")
        else:
            print(f"❌ 滑点配置错误，期望: {expected_slippages}，实际: {retry_slippages}")
        
        # 测试每个滑点值的路由获取
        print("\n🔍 测试各个滑点值的路由获取:")
        
        for i, slippage in enumerate(retry_slippages):
            print(f"\n   测试滑点 {i+1}/{len(retry_slippages)}: {slippage}%")
            
            try:
                routes_data = await client.get_routes(
                    token_in=token_in,
                    token_out=token_out,
                    amount_in=amount_in,
                    slippage=slippage,
                    is_native_in=False,
                    save_gas=False,
                    excluded_sources="bebop",
                    use_proxy=False  # 真实交易模式
                )
                
                if "error" in routes_data:
                    print(f"   ❌ 滑点 {slippage}% 获取路由失败: {routes_data['error']}")
                else:
                    route_summary = routes_data.get("data", {}).get("routeSummary", {})
                    amount_out = route_summary.get("amountOut", "N/A")
                    print(f"   ✅ 滑点 {slippage}% 获取路由成功，预期输出: {amount_out}")
                    
            except Exception as e:
                print(f"   ❌ 滑点 {slippage}% 测试出错: {str(e)}")
        
        print("\n🎯 滑点策略总结:")
        print("   - 0.1%: 最低滑点，适合稳定币交易")
        print("   - 0.3%: 低滑点，适合主流代币")
        print("   - 0.6%: 中等滑点，适合一般代币")
        print("   - 1.0%: 较高滑点，适合波动性代币")
        print("   - 5.0%: 最高滑点，适合高波动性或流动性差的代币")
        
        print("\n💡 真实交易流程:")
        print("   1. 首先尝试 0.1% 滑点")
        print("   2. 如果失败，尝试 0.3% 滑点")
        print("   3. 如果失败，尝试 0.6% 滑点")
        print("   4. 如果失败，尝试 1.0% 滑点")
        print("   5. 如果失败，尝试 5.0% 滑点")
        print("   6. 如果所有滑点都失败，返回错误")
        
        print("\n🎉 滑点测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_slippage_settings())
