2025-05-16 15:26:03,292 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:26:04,501 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 15:26:05,982 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 15:26:05,982 - token_change_tracker - ERROR - 跟踪代币变化时出错: invalid literal for int() with base 16: b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x11\xe1\xa3\x00'
2025-05-16 15:26:05,982 - token_change_tracker - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\network\tx_token_change_tracker.py", line 362, in track_token_changes
    amount = int(log["data"], 16)
             ^^^^^^^^^^^^^^^^^^^^
ValueError: invalid literal for int() with base 16: b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x11\xe1\xa3\x00'

2025-05-16 15:46:26,870 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:28,352 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 15:46:29,782 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 15:46:29,790 - token_change_tracker - WARNING - 本地文件中未包含ETH链的代币信息
2025-05-16 15:46:29,794 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:29,795 - token_change_tracker - INFO - 使用代理: {'http': 'http://746649bb3e9aa558ccdc:<EMAIL>:823', 'https': 'http://746649bb3e9aa558ccdc:<EMAIL>:823'}
2025-05-16 15:46:33,784 - token_change_tracker - WARNING - API请求失败或返回无效数据: {"status":"0","message":"NOTOK","result":"Sorry, it looks like you are trying to access an API Pro endpoint. Contact us to upgrade to API Pro."}
2025-05-16 15:46:33,793 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:37,896 - token_change_tracker - INFO - 通过Web3获取代币信息: Rai Reflex Index(RAI), 精度: 18
2025-05-16 15:46:37,899 - token_change_tracker - WARNING - 本地文件中未包含ETH链的代币信息
2025-05-16 15:46:37,905 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:37,905 - token_change_tracker - INFO - 使用代理: {'http': 'http://746649bb3e9aa558ccdc:<EMAIL>:823', 'https': 'http://746649bb3e9aa558ccdc:<EMAIL>:823'}
2025-05-16 15:46:41,559 - token_change_tracker - WARNING - API请求失败或返回无效数据: {"status":"0","message":"NOTOK","result":"Sorry, it looks like you are trying to access an API Pro endpoint. Contact us to upgrade to API Pro."}
2025-05-16 15:46:41,566 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:45,870 - token_change_tracker - INFO - 通过Web3获取代币信息: Tether USD(USDT), 精度: 6
2025-05-16 15:46:46,720 - token_change_tracker - WARNING - 本地文件中未包含ETH链的代币信息
2025-05-16 15:46:46,725 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:46,725 - token_change_tracker - INFO - 使用代理: {'http': 'http://746649bb3e9aa558ccdc:<EMAIL>:823', 'https': 'http://746649bb3e9aa558ccdc:<EMAIL>:823'}
2025-05-16 15:46:50,109 - token_change_tracker - WARNING - API请求失败或返回无效数据: {"status":"0","message":"NOTOK","result":"Sorry, it looks like you are trying to access an API Pro endpoint. Contact us to upgrade to API Pro."}
2025-05-16 15:46:50,119 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:46:53,787 - token_change_tracker - INFO - 通过Web3获取代币信息: Rai Reflex Index(RAI), 精度: 18
2025-05-16 15:46:53,788 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 RAI
2025-05-16 15:46:53,789 - token_change_tracker - INFO - 交易 0x4471b25a0106e4f7fbbade982e02a6e5f7cd10063e5cc81b587b36c2ffa2e669 的代币变化分析完成
2025-05-16 15:46:53,789 - token_change_tracker - INFO - 状态: 成功
2025-05-16 15:46:53,790 - token_change_tracker - INFO - 转入: 97.9489721335032 RAI
2025-05-16 15:46:53,790 - token_change_tracker - INFO - 转出: 300.0 USDT
2025-05-16 15:46:53,790 - token_change_tracker - INFO - 净变化: 97.9489721335032 RAI, -300.0 USDT, -0.000367922286396717 ETH
2025-05-16 15:46:53,790 - token_change_tracker - INFO - 结果已保存到: data\network\tx_0x4471b2_ethereum.json
2025-05-16 15:51:39,775 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:51:40,487 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 15:51:42,382 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 15:51:42,384 - token_change_tracker - WARNING - 本地文件中未包含ETH链的代币信息
2025-05-16 15:51:42,390 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:51:47,662 - token_change_tracker - INFO - 通过Web3获取代币信息: Rai Reflex Index(RAI), 精度: 18
2025-05-16 15:51:47,662 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 RAI
2025-05-16 15:56:12,597 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 15:56:14,050 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 15:56:14,836 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 15:56:14,839 - token_change_tracker - INFO - 从本地获取到代币信息: Rai Reflex Index(Rai Reflex Index), 精度: 18
2025-05-16 15:56:14,839 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 Rai Reflex Index
2025-05-16 16:01:19,179 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:01:20,633 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 16:01:21,698 - token_change_tracker - INFO - 从本地获取到代币信息: Rai Reflex Index(Rai Reflex Index), 精度: 18
2025-05-16 16:01:21,699 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 Rai Reflex Index
2025-05-16 16:01:52,097 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:01:52,796 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 16:01:53,861 - token_change_tracker - INFO - 从本地获取到代币信息: DAI(DAI), 精度: 18
2025-05-16 16:01:53,861 - token_change_tracker - INFO - 交易最后接收的代币: 300.0431129073008 DAI
2025-05-16 16:05:01,679 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:05:02,315 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:05:02,812 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:05:31,573 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:05:32,211 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:05:32,433 - token_change_tracker - WARNING - 本地文件中未包含POLYGON链的代币信息
2025-05-16 16:05:32,437 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:05:33,879 - token_change_tracker - INFO - 通过Web3获取代币信息: Rai Reflex Index (PoS)(RAI), 精度: 18
2025-05-16 16:05:33,879 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 RAI
2025-05-16 16:06:10,385 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:06:11,030 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:06:44,390 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:06:45,021 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:06:56,391 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:06:57,045 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:06:57,461 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:07:16,239 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:07:16,878 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:07:17,322 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:07:17,329 - token_change_tracker - WARNING - 本地文件中未包含POLYGON链的代币信息
2025-05-16 16:07:17,333 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:07:18,761 - token_change_tracker - INFO - 通过Web3获取代币信息: Morpheus.Network (PoS)(MNW), 精度: 18
2025-05-16 16:07:18,762 - token_change_tracker - INFO - 交易最后接收的代币: 2199.9001099329766 MNW
2025-05-16 16:07:38,963 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:07:39,596 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:07:39,827 - token_change_tracker - WARNING - 本地文件中未包含POLYGON链的代币信息
2025-05-16 16:07:39,831 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:07:41,294 - token_change_tracker - INFO - 通过Web3获取代币信息: Wrapped Polygon Ecosystem Token(WPOL), 精度: 18
2025-05-16 16:07:41,295 - token_change_tracker - INFO - 交易最后接收的代币: 352.3024728270684 WPOL
2025-05-16 16:08:38,646 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:08:39,278 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:08:39,787 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:08:39,791 - token_change_tracker - WARNING - 本地文件中未包含POLYGON链的代币信息
2025-05-16 16:08:39,795 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:08:41,273 - token_change_tracker - INFO - 通过Web3获取代币信息: Morpheus.Network (PoS)(MNW), 精度: 18
2025-05-16 16:08:41,273 - token_change_tracker - INFO - 交易最后接收的代币: 2199.9001099329766 MNW
2025-05-16 16:09:46,811 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:09:47,451 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:09:47,879 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:09:47,888 - token_change_tracker - INFO - 从本地获取到代币信息: MNW(MNW), 精度: 18
2025-05-16 16:09:47,889 - token_change_tracker - INFO - 交易最后接收的代币: 2199.9001099329766 MNW
2025-05-16 16:27:54,444 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:27:56,026 - token_change_tracker - INFO - 成功连接到ethereum的RPC: https://ethereum-rpc.publicnode.com
2025-05-16 16:27:58,444 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:27:58,452 - token_change_tracker - WARNING - 本地文件中未包含ETH链的代币信息
2025-05-16 16:27:58,457 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:28:02,146 - token_change_tracker - INFO - 通过Web3获取代币信息: Rai Reflex Index(RAI), 精度: 18
2025-05-16 16:28:02,147 - token_change_tracker - INFO - 交易最后接收的代币: 97.9489721335032 RAI
2025-05-16 16:28:25,551 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:28:26,624 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:28:27,077 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:28:27,083 - token_change_tracker - WARNING - 本地文件中未包含POLYGON链的代币信息
2025-05-16 16:28:27,092 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:28:28,600 - token_change_tracker - INFO - 通过Web3获取代币信息: SUKU (PoS)(SUKU), 精度: 18
2025-05-16 16:28:28,600 - token_change_tracker - INFO - 交易最后接收的代币: 2979.6814465965103 SUKU
2025-05-16 16:29:21,333 - token_change_tracker - INFO - 配置文件加载成功
2025-05-16 16:29:21,970 - token_change_tracker - INFO - 成功连接到polygon的RPC: https://polygon-rpc.com
2025-05-16 16:29:22,410 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-05-16 16:29:22,417 - token_change_tracker - INFO - 从本地获取到代币信息: SUKU(SUKU), 精度: 18
2025-05-16 16:29:22,417 - token_change_tracker - INFO - 交易最后接收的代币: 2979.6814465965103 SUKU
2025-06-14 15:26:38,078 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:38,083 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:38,843 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第1轮第1个RPC，总尝试次数: 1/20
2025-06-14 15:26:38,849 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:38,853 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:40,840 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第1轮第2个RPC，总尝试次数: 2/20
2025-06-14 15:26:40,845 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:40,850 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:42,674 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第1轮第3个RPC，总尝试次数: 3/20
2025-06-14 15:26:42,679 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:42,684 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:43,564 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第1轮第4个RPC，总尝试次数: 4/20
2025-06-14 15:26:43,569 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:43,574 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:44,487 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第1轮第5个RPC，总尝试次数: 5/20
2025-06-14 15:26:44,493 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:44,498 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:45,057 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第2轮第1个RPC，总尝试次数: 6/20
2025-06-14 15:26:45,062 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:45,067 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:45,707 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第2轮第2个RPC，总尝试次数: 7/20
2025-06-14 15:26:45,717 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:45,722 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:46,186 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第2轮第3个RPC，总尝试次数: 8/20
2025-06-14 15:26:46,191 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:46,195 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:46,607 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第2轮第4个RPC，总尝试次数: 9/20
2025-06-14 15:26:46,613 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:46,618 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:47,341 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第2轮第5个RPC，总尝试次数: 10/20
2025-06-14 15:26:47,347 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:47,352 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:48,498 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第3轮第1个RPC，总尝试次数: 11/20
2025-06-14 15:26:48,507 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:48,512 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:50,217 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第3轮第2个RPC，总尝试次数: 12/20
2025-06-14 15:26:50,223 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:50,228 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:53,550 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第3轮第3个RPC，总尝试次数: 13/20
2025-06-14 15:26:53,556 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:53,561 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:54,773 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第3轮第4个RPC，总尝试次数: 14/20
2025-06-14 15:26:54,778 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:54,783 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:55,621 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第3轮第5个RPC，总尝试次数: 15/20
2025-06-14 15:26:55,631 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:55,635 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:56,093 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第4轮第1个RPC，总尝试次数: 16/20
2025-06-14 15:26:56,099 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:56,104 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:57,124 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第4轮第2个RPC，总尝试次数: 17/20
2025-06-14 15:26:57,134 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:57,139 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:57,886 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第4轮第3个RPC，总尝试次数: 18/20
2025-06-14 15:26:57,896 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:57,901 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:58,606 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第4轮第4个RPC，总尝试次数: 19/20
2025-06-14 15:26:58,615 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:58,622 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:59,441 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第4轮第5个RPC，总尝试次数: 20/20
2025-06-14 15:26:59,449 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:26:59,449 - token_change_tracker - ERROR - 已达到最大重试次数(20)，放弃重试
2025-06-14 15:26:59,449 - trade_VAI - WARNING - VAI: 第1次获取代币数量失败: 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 15:26:59,449 - trade_VAI - INFO - VAI: 等待5秒后重试...
2025-06-14 15:27:04,457 - trade_VAI - INFO - VAI: 第2次尝试获取代币数量...
2025-06-14 15:27:04,457 - trade_VAI - INFO - VAI: 使用tx_token_change_tracker获取交易 0x8c0aca48c538952da2a2f4dcbcf3f4970a60da241d8e194fd4318471ffdd3a63 的代币数量...
2025-06-14 15:27:04,463 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:04,467 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:06,382 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第1轮第1个RPC，总尝试次数: 1/20
2025-06-14 15:27:06,387 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:06,392 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:08,954 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第1轮第2个RPC，总尝试次数: 2/20
2025-06-14 15:27:08,963 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:08,969 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:09,720 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第1轮第3个RPC，总尝试次数: 3/20
2025-06-14 15:27:09,726 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:09,731 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:10,946 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第1轮第4个RPC，总尝试次数: 4/20
2025-06-14 15:27:10,951 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:10,956 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:11,725 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第1轮第5个RPC，总尝试次数: 5/20
2025-06-14 15:27:11,734 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:11,739 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:12,503 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第2轮第1个RPC，总尝试次数: 6/20
2025-06-14 15:27:12,509 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:12,514 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:13,694 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第2轮第2个RPC，总尝试次数: 7/20
2025-06-14 15:27:13,699 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:13,704 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:14,824 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第2轮第3个RPC，总尝试次数: 8/20
2025-06-14 15:27:14,830 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:14,834 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:15,292 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第2轮第4个RPC，总尝试次数: 9/20
2025-06-14 15:27:15,296 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:15,301 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:17,400 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第2轮第5个RPC，总尝试次数: 10/20
2025-06-14 15:27:17,405 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:17,410 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:18,227 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第3轮第1个RPC，总尝试次数: 11/20
2025-06-14 15:27:18,235 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:18,241 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:18,902 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第3轮第2个RPC，总尝试次数: 12/20
2025-06-14 15:27:18,910 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:18,918 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:19,935 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第3轮第3个RPC，总尝试次数: 13/20
2025-06-14 15:27:19,941 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:19,946 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:20,544 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第3轮第4个RPC，总尝试次数: 14/20
2025-06-14 15:27:20,553 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:20,559 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:21,854 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第3轮第5个RPC，总尝试次数: 15/20
2025-06-14 15:27:21,861 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:21,866 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:22,466 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第4轮第1个RPC，总尝试次数: 16/20
2025-06-14 15:27:22,475 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:22,481 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:23,039 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第4轮第2个RPC，总尝试次数: 17/20
2025-06-14 15:27:23,044 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:23,049 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:23,720 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第4轮第3个RPC，总尝试次数: 18/20
2025-06-14 15:27:23,725 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:23,730 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:25,340 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第4轮第4个RPC，总尝试次数: 19/20
2025-06-14 15:27:25,346 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:25,351 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:25,843 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第4轮第5个RPC，总尝试次数: 20/20
2025-06-14 15:27:25,848 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:25,848 - token_change_tracker - ERROR - 已达到最大重试次数(20)，放弃重试
2025-06-14 15:27:25,849 - trade_VAI - WARNING - VAI: 第2次获取代币数量失败: 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 15:27:25,849 - trade_VAI - INFO - VAI: 等待5秒后重试...
2025-06-14 15:27:30,858 - trade_VAI - INFO - VAI: 第3次尝试获取代币数量...
2025-06-14 15:27:30,858 - trade_VAI - INFO - VAI: 使用tx_token_change_tracker获取交易 0x8c0aca48c538952da2a2f4dcbcf3f4970a60da241d8e194fd4318471ffdd3a63 的代币数量...
2025-06-14 15:27:30,864 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:30,869 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:31,373 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第1轮第1个RPC，总尝试次数: 1/20
2025-06-14 15:27:31,382 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:31,388 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:33,040 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第1轮第2个RPC，总尝试次数: 2/20
2025-06-14 15:27:33,051 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:33,056 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:33,839 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第1轮第3个RPC，总尝试次数: 3/20
2025-06-14 15:27:33,844 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:33,849 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:34,389 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第1轮第4个RPC，总尝试次数: 4/20
2025-06-14 15:27:34,395 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:34,400 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:35,061 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第1轮第5个RPC，总尝试次数: 5/20
2025-06-14 15:27:35,066 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:35,071 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:36,735 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第2轮第1个RPC，总尝试次数: 6/20
2025-06-14 15:27:36,741 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:36,746 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:37,945 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第2轮第2个RPC，总尝试次数: 7/20
2025-06-14 15:27:37,951 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:37,956 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:38,979 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第2轮第3个RPC，总尝试次数: 8/20
2025-06-14 15:27:38,987 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:38,992 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:39,494 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第2轮第4个RPC，总尝试次数: 9/20
2025-06-14 15:27:39,499 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:39,505 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:41,049 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第2轮第5个RPC，总尝试次数: 10/20
2025-06-14 15:27:41,055 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:41,060 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:41,732 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第3轮第1个RPC，总尝试次数: 11/20
2025-06-14 15:27:41,741 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:41,746 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:42,864 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第3轮第2个RPC，总尝试次数: 12/20
2025-06-14 15:27:42,869 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:42,874 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:44,681 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第3轮第3个RPC，总尝试次数: 13/20
2025-06-14 15:27:44,691 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:44,697 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:46,208 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第3轮第4个RPC，总尝试次数: 14/20
2025-06-14 15:27:46,217 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:46,222 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:48,077 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第3轮第5个RPC，总尝试次数: 15/20
2025-06-14 15:27:48,083 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:48,088 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:49,748 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://ethereum-rpc.publicnode.com)，当前是第4轮第1个RPC，总尝试次数: 16/20
2025-06-14 15:27:49,758 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:49,763 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:50,809 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://1rpc.io/eth)，当前是第4轮第2个RPC，总尝试次数: 17/20
2025-06-14 15:27:50,817 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:50,822 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:51,501 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://rpc.mevblocker.io)，当前是第4轮第3个RPC，总尝试次数: 18/20
2025-06-14 15:27:51,506 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:51,510 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:52,397 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.llamarpc.com)，当前是第4轮第4个RPC，总尝试次数: 19/20
2025-06-14 15:27:52,402 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:52,407 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:53,512 - token_change_tracker - WARNING - 无法通过代理连接到ethereum的RPC(https://eth.blockrazor.xyz)，当前是第4轮第5个RPC，总尝试次数: 20/20
2025-06-14 15:27:53,524 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 15:27:53,524 - token_change_tracker - ERROR - 已达到最大重试次数(20)，放弃重试
2025-06-14 15:27:53,525 - trade_VAI - WARNING - VAI: 第3次获取代币数量失败: 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 15:27:53,525 - trade_VAI - ERROR - VAI: 在3次尝试后仍然无法获取代币数量: 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 15:27:53,525 - root - ERROR - 执行交易时出错: 买入操作失败: 无法获取代币数量: 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 15:27:53,526 - root - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 599, in execute_trade_in_thread
    raise Exception(f"买入操作失败: {error_msg}")
Exception: 买入操作失败: 无法获取代币数量: 无法连接到ethereum的RPC（已尝试所有可用RPC）

2025-06-14 17:58:11,466 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:11,470 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:23,539 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:24,614 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:25,340 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:26,391 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:27,142 - token_change_tracker - INFO - 所有RPC连接失败，第1次重试...
2025-06-14 17:58:32,152 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:32,156 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:32,626 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:33,675 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:35,116 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:35,659 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:36,200 - token_change_tracker - INFO - 所有RPC连接失败，第2次重试...
2025-06-14 17:58:41,212 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:41,217 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:43,871 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:44,611 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:45,059 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:45,547 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:46,775 - token_change_tracker - INFO - 所有RPC连接失败，第3次重试...
2025-06-14 17:58:51,783 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:51,788 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:52,263 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:53,009 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:54,138 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:54,742 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:58:55,218 - token_change_tracker - ERROR - 无法连接到ethereum的RPC（已尝试所有可用RPC）
2025-06-14 17:59:38,657 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:59:38,662 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 17:59:47,712 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://rpc.mevblocker.io
2025-06-14 17:59:54,712 - token_change_tracker - ERROR - 获取最后接收代币时出错: 415 Client Error: Unsupported Media Type for url: https://rpc.mevblocker.io/
2025-06-14 17:59:54,719 - token_change_tracker - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\network\tx_token_change_tracker.py", line 401, in get_last_received_token
    tx_receipt = web3.eth.get_transaction_receipt(tx_hash)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\eth\eth.py", line 479, in get_transaction_receipt
    return self._transaction_receipt(transaction_hash)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\module.py", line 75, in caller
    result = w3.manager.request_blocking(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\manager.py", line 325, in request_blocking
    response = self._make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\manager.py", line 213, in _make_request
    return request_func(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\gas_price_strategy.py", line 101, in middleware
    return make_request(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\formatting.py", line 126, in middleware
    response = make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\attrdict.py", line 43, in middleware
    response = make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\formatting.py", line 126, in middleware
    response = make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\formatting.py", line 126, in middleware
    response = make_request(method, params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\buffered_gas_estimate.py", line 43, in middleware
    return make_request(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\middleware\exception_retry_request.py", line 125, in middleware
    return make_request(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\providers\rpc.py", line 90, in make_request
    raw_response = make_post_request(
                   ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\web3\_utils\request.py", line 115, in make_post_request
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 415 Client Error: Unsupported Media Type for url: https://rpc.mevblocker.io/

2025-06-14 18:01:03,890 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:01:03,895 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:01:07,179 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://ethereum-rpc.publicnode.com
2025-06-14 18:01:10,718 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-06-14 18:01:10,718 - token_change_tracker - INFO - 从本地获取到代币信息: SPORK(SPORK), 精度: 18
2025-06-14 18:01:10,718 - token_change_tracker - INFO - 交易最后接收的代币: 1644.260378513302492501 SPORK
2025-06-14 18:02:22,490 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:02:22,495 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:02:26,105 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://1rpc.io/eth
2025-06-14 18:02:28,001 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-06-14 18:02:28,003 - token_change_tracker - INFO - 从本地获取到代币信息: SPORK(SPORK), 精度: 18
2025-06-14 18:02:28,004 - token_change_tracker - INFO - 交易最后接收的代币: 1644.260378513302492501 SPORK
2025-06-14 18:18:20,446 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:18:20,451 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 18:18:23,648 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://rpc.mevblocker.io
2025-06-14 18:18:25,148 - token_change_tracker - INFO - 使用交易from地址作为用户地址: ******************************************
2025-06-14 18:18:25,148 - token_change_tracker - INFO - 从本地获取到代币信息: DWEB(DWEB), 精度: 18
2025-06-14 18:18:25,148 - token_change_tracker - INFO - 交易最后接收的代币: 8482.413034769330424562 DWEB
2025-06-14 20:17:35,107 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 20:17:35,112 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 20:17:40,724 - token_change_tracker - INFO - 成功连接到polygon RPC: https://1rpc.io/matic
2025-06-14 20:17:42,098 - token_change_tracker - INFO - 从本地获取到代币信息: DMCC(DMCC), 精度: 18
2025-06-14 20:17:42,099 - token_change_tracker - INFO - 交易最后接收的代币: 67.129922373033784441 DMCC
2025-06-14 20:17:42,099 - trade_DMCC - INFO - DMCC: 从tx_token_change_tracker成功获取到代币数量: 67.129922373033784441
2025-06-14 20:17:42,099 - trade_DMCC - INFO - 交易执行成功:
2025-06-14 20:17:42,100 - trade_DMCC - INFO -   交易哈希: 0x808ee10ab52e1abff74576ec908a17a0a686e604169e1d248494cf9a29e7eb60
2025-06-14 20:17:42,100 - trade_DMCC - INFO -   实际输出数量: 67.12992237303378 DMCC
2025-06-14 20:17:42,100 - trade_DMCC - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-06-14 20:17:42,100 - trade_DMCC - INFO - ================================================================================
2025-06-14 20:17:42,100 - trade_DMCC - INFO - 开始执行 DMCC 桥接操作 - 时间: 2025-06-14 20:17:42
2025-06-14 20:17:42,101 - trade_DMCC - INFO - 桥接方向: polygon_to_ethereum
2025-06-14 20:17:42,101 - trade_DMCC - INFO - 代币数量: 67.129922373033784441 DMCC
2025-06-14 20:17:44,537 - trade_DMCC - INFO - 从Polygon桥接到以太坊: 67.129922373033784441 DMCC
2025-06-14 20:17:44,539 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 20:17:44,550 - root - INFO - 金额转换结果: 67.129922373033784441 -> 67129922373033784441
2025-06-14 20:17:44,877 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.452825746 Gwei
2025-06-14 20:17:44,879 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-14 20:17:45,790 - root - INFO - 开始从Polygon提取 67.12992237303378 DMCC 到以太坊
2025-06-14 20:17:46,786 - root - INFO - 估算gas限制: 47014，使用56416（含20%缓冲）
2025-06-14 20:17:47,452 - root - INFO - Gas费用信息:
2025-06-14 20:17:47,453 - root - INFO - Base fee: 1.450980698 Gwei
2025-06-14 20:17:47,453 - root - INFO - Priority fee: 250 Gwei
2025-06-14 20:17:47,454 - root - INFO - Max fee: 252.901961396 Gwei
2025-06-14 20:17:54,109 - root - ERROR - 从Polygon转移到以太坊时出错: object AttributeDict can't be used in 'await' expression
2025-06-14 20:18:12,117 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 20:18:12,118 - root - INFO - 金额转换结果: 67.129922373033784441 -> 67129922373033784441
2025-06-14 20:18:12,432 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.438653367 Gwei
2025-06-14 20:18:12,432 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-14 20:18:13,025 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:13,026 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:49,037 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 20:18:49,039 - root - INFO - 金额转换结果: 67.129922373033784441 -> 67129922373033784441
2025-06-14 20:18:50,938 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.455401577 Gwei
2025-06-14 20:18:50,940 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-14 20:18:51,729 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:51,729 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:51,730 - trade_DMCC - ERROR - 桥接操作失败: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:51,730 - trade_DMCC - ERROR - 桥接操作失败: 余额不足，需要 67.12992237303378 DMCC，但只有 0.0 DMCC
2025-06-14 20:18:51,730 - trade_DMCC - INFO - 交易执行完成，耗时: 125.78秒
2025-06-14 20:18:51,731 - trade_DMCC - INFO - ================================================================================
2025-06-14 20:18:51,731 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DMCC_20250614_201851_new.json
2025-06-14 20:18:51,732 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DMCC_20250614_201851_new.json
2025-06-14 20:18:54,231 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 20:19:34,297 - ETHGasTracker - ERROR - 从Etherscan获取gas价格时出错: 
2025-06-14 20:19:36,873 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCB86050>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:19:41,263 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCB84E50>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:19:46,007 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEE310>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:19:46,588 - ETHGasTracker - ERROR - 代理请求失败: SOCKSHTTPSConnectionPool(host='ethereum-rpc.publicnode.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEFB90>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset'))
2025-06-14 20:19:46,589 - ETHGasTracker - ERROR - 无法连接到以太坊节点
2025-06-14 20:19:47,936 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD9B50>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /eth
2025-06-14 20:19:51,406 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCADA410>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /eth
2025-06-14 20:19:57,478 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEB7D0>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /eth
2025-06-14 20:19:59,108 - ETHGasTracker - ERROR - 代理请求失败: SOCKSHTTPSConnectionPool(host='1rpc.io', port=443): Max retries exceeded with url: /eth (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEAA50>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset'))
2025-06-14 20:20:00,916 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEE410>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:04,344 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAEEED0>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:10,241 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD9390>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:11,156 - ETHGasTracker - ERROR - 代理请求失败: SOCKSHTTPSConnectionPool(host='rpc.mevblocker.io', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAB7F50>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset'))
2025-06-14 20:20:12,786 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAFAA10>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:15,866 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAFBCD0>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:21,349 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAFAE50>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:22,223 - ETHGasTracker - ERROR - 代理请求失败: SOCKSHTTPSConnectionPool(host='eth.llamarpc.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD8450>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset'))
2025-06-14 20:20:23,324 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD5290>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:26,022 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD6D10>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:31,149 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD4A10>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset')': /
2025-06-14 20:20:32,546 - ETHGasTracker - ERROR - 代理请求失败: SOCKSHTTPSConnectionPool(host='eth.blockrazor.xyz', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x00000295BCAD81D0>: Failed to establish a new connection: 0x02: Connection not allowed by ruleset'))
2025-06-14 20:20:32,547 - ETHGasTracker - ERROR - 所有备用RPC节点都无法连接
2025-06-14 20:20:32,548 - arb_finder_main - WARNING - 无法获取gas价格，将继续执行任务
2025-06-14 20:29:44,885 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 20:30:18,190 - arb_finder_main - INFO - 当前ETH gas价格: 0.54 Gwei
2025-06-14 20:30:18,190 - arb_finder_main - INFO - Gas价格较低 (0.54 Gwei)，开始执行套利任务
2025-06-14 20:40:32,532 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 20:41:07,228 - arb_finder_main - INFO - 当前ETH gas价格: 0.45 Gwei
2025-06-14 20:41:07,228 - arb_finder_main - INFO - Gas价格较低 (0.45 Gwei)，开始执行套利任务
2025-06-14 20:50:38,842 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 20:51:11,986 - arb_finder_main - INFO - 当前ETH gas价格: 0.48 Gwei
2025-06-14 20:51:11,987 - arb_finder_main - INFO - Gas价格较低 (0.48 Gwei)，开始执行套利任务
2025-06-14 21:00:42,214 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 21:01:15,091 - arb_finder_main - INFO - 当前ETH gas价格: 0.56 Gwei
2025-06-14 21:01:15,091 - arb_finder_main - INFO - Gas价格较低 (0.56 Gwei)，开始执行套利任务
2025-06-14 21:07:29,922 - trade_SG - INFO - ================================================================================
2025-06-14 21:07:29,923 - trade_SG - INFO - 开始执行 SG 买入交易 - 时间: 2025-06-14 21:07:29
2025-06-14 21:07:29,924 - trade_SG - INFO - 链: polygon, 投入金额: 166.67 USDT
2025-06-14 21:07:29,925 - trade_SG - INFO - 代币地址: ******************************************
2025-06-14 21:07:29,925 - trade_SG - INFO - SG: 将在polygon链上执行买入，代币地址: ******************************************
2025-06-14 21:07:29,926 - trade_SG - INFO - SG: 准备使用KyberSwap在polygon上执行166.67USDT买入SG交易
2025-06-14 21:08:09,344 - trade_SG - INFO - SG: 使用tx_token_change_tracker获取交易 0x49ecab8d28315866e4cf3c128f452c959f42b315d254902d9f2c114843d4f020 的代币数量...
2025-06-14 21:08:09,349 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 21:08:09,354 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 21:08:11,800 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 21:08:28,145 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 21:08:33,397 - token_change_tracker - INFO - 成功连接到polygon RPC: https://polygon-mainnet.public.blastapi.io
2025-06-14 21:08:33,825 - token_change_tracker - INFO - 从本地获取到代币信息: SG(SG), 精度: 18
2025-06-14 21:08:33,826 - token_change_tracker - INFO - 交易最后接收的代币: 2189.704174028201940331 SG
2025-06-14 21:08:33,826 - trade_SG - INFO - SG: 从tx_token_change_tracker成功获取到代币数量: 2189.704174028201940331
2025-06-14 21:08:33,827 - trade_SG - INFO - 交易执行成功:
2025-06-14 21:08:33,827 - trade_SG - INFO -   交易哈希: 0x49ecab8d28315866e4cf3c128f452c959f42b315d254902d9f2c114843d4f020
2025-06-14 21:08:33,827 - trade_SG - INFO -   实际输出数量: 2189.704174028202 SG
2025-06-14 21:08:33,827 - trade_SG - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-06-14 21:08:33,827 - trade_SG - INFO - ================================================================================
2025-06-14 21:08:33,827 - trade_SG - INFO - 开始执行 SG 桥接操作 - 时间: 2025-06-14 21:08:33
2025-06-14 21:08:33,827 - trade_SG - INFO - 桥接方向: polygon_to_ethereum
2025-06-14 21:08:33,828 - trade_SG - INFO - 代币数量: 2189.704174028201940331 SG
2025-06-14 21:08:37,621 - trade_SG - INFO - 从Polygon桥接到以太坊: 2189.704174028201940331 SG
2025-06-14 21:08:37,623 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 21:08:37,625 - root - INFO - 金额转换结果: 2189.704174028201940331 -> 2189704174028201940331
2025-06-14 21:08:38,180 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.575797101 Gwei
2025-06-14 21:08:38,182 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'SocialGood (PoS)', 'symbol': 'SG', 'decimals': 18, 'chainId': 137}
2025-06-14 21:08:39,684 - root - INFO - 开始从Polygon提取 2189.704174028202 SG 到以太坊
2025-06-14 21:08:41,126 - root - INFO - 估算gas限制: 47014，使用56416（含20%缓冲）
2025-06-14 21:08:41,526 - root - INFO - Gas费用信息:
2025-06-14 21:08:41,527 - root - INFO - Base fee: 5.341266695 Gwei
2025-06-14 21:08:41,528 - root - INFO - Priority fee: 250 Gwei
2025-06-14 21:08:41,528 - root - INFO - Max fee: 260.68253339 Gwei
2025-06-14 21:08:48,537 - root - ERROR - 从Polygon转移到以太坊时出错: object AttributeDict can't be used in 'await' expression
2025-06-14 21:09:06,547 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 21:09:06,550 - root - INFO - 金额转换结果: 2189.704174028201940331 -> 2189704174028201940331
2025-06-14 21:09:07,303 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.600103751 Gwei
2025-06-14 21:09:07,304 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'SocialGood (PoS)', 'symbol': 'SG', 'decimals': 18, 'chainId': 137}
2025-06-14 21:09:08,265 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:08,265 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:44,277 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 21:09:44,279 - root - INFO - 金额转换结果: 2189.704174028201940331 -> 2189704174028201940331
2025-06-14 21:09:45,663 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.602663483 Gwei
2025-06-14 21:09:45,665 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'SocialGood (PoS)', 'symbol': 'SG', 'decimals': 18, 'chainId': 137}
2025-06-14 21:09:46,926 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:46,926 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:46,927 - trade_SG - ERROR - 桥接操作失败: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:46,927 - trade_SG - ERROR - 桥接操作失败: 余额不足，需要 2189.704174028202 SG，但只有 2.0518e-14 SG
2025-06-14 21:09:46,927 - trade_SG - INFO - 交易执行完成，耗时: 137.00秒
2025-06-14 21:09:46,927 - trade_SG - INFO - ================================================================================
2025-06-14 21:09:46,928 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\SG_20250614_210946_new.json
2025-06-14 21:09:46,929 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\SG_20250614_210946_new.json
2025-06-14 21:12:43,757 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 21:13:17,088 - arb_finder_main - INFO - 当前ETH gas价格: 0.62 Gwei
2025-06-14 21:13:17,089 - arb_finder_main - INFO - Gas价格较低 (0.62 Gwei)，开始执行套利任务
2025-06-14 21:23:08,155 - arb_finder_main - INFO - 用户中断操作
2025-06-14 22:05:14,778 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 22:05:14,783 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 22:05:17,439 - token_change_tracker - INFO - 配置文件加载成功
2025-06-14 22:05:21,003 - token_change_tracker - INFO - 成功连接到polygon RPC: https://polygon-rpc.com
2025-06-14 22:05:22,344 - token_change_tracker - INFO - 从本地获取到代币信息: DYDX(DYDX), 精度: 18
2025-06-14 22:05:22,344 - token_change_tracker - INFO - 交易最后接收的代币: 481.994409511640850529 DYDX
2025-06-14 22:05:22,344 - trade_DYDX - INFO - DYDX: 从tx_token_change_tracker成功获取到代币数量: 481.994409511640850529
2025-06-14 22:05:22,345 - trade_DYDX - INFO - 交易执行成功:
2025-06-14 22:05:22,345 - trade_DYDX - INFO -   交易哈希: 0x7226a32aba9705c9a2ec9bf671a8ef0e3961300f0333c3308218eb45d4498f7c
2025-06-14 22:05:22,345 - trade_DYDX - INFO -   实际输出数量: 481.99440951164087 DYDX
2025-06-14 22:05:22,345 - trade_DYDX - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-06-14 22:05:22,345 - trade_DYDX - INFO - ================================================================================
2025-06-14 22:05:22,345 - trade_DYDX - INFO - 开始执行 DYDX 桥接操作 - 时间: 2025-06-14 22:05:22
2025-06-14 22:05:22,346 - trade_DYDX - INFO - 桥接方向: polygon_to_ethereum
2025-06-14 22:05:22,346 - trade_DYDX - INFO - 代币数量: 481.994409511640850529 DYDX
2025-06-14 22:05:26,232 - trade_DYDX - INFO - 从Polygon桥接到以太坊: 481.994409511640850529 DYDX
2025-06-14 22:05:26,234 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 22:05:26,245 - root - INFO - 金额转换结果: 481.994409511640850529 -> 481994409511640850529
2025-06-14 22:05:26,723 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.776338446 Gwei
2025-06-14 22:05:26,726 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'dYdX (PoS)', 'symbol': 'DYDX', 'decimals': 18, 'chainId': 137}
2025-06-14 22:05:28,447 - root - INFO - 开始从Polygon提取 481.99440951164087 DYDX 到以太坊
2025-06-14 22:05:31,703 - root - INFO - 估算gas限制: 47296，使用56755（含20%缓冲）
2025-06-14 22:05:32,443 - root - INFO - Gas费用信息:
2025-06-14 22:05:32,444 - root - INFO - Base fee: 1.089414251 Gwei
2025-06-14 22:05:32,445 - root - INFO - Priority fee: 250 Gwei
2025-06-14 22:05:32,446 - root - INFO - Max fee: 252.178828502 Gwei
2025-06-14 22:05:40,298 - root - ERROR - 从Polygon转移到以太坊时出错: object AttributeDict can't be used in 'await' expression
2025-06-14 22:05:40,303 - root - WARNING - Polygon->ETH 桥接尝试 1 失败: object AttributeDict can't be used in 'await' expression
2025-06-14 22:05:40,304 - root - INFO - 等待 18 秒后重试...
2025-06-14 22:06:00,733 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 22:06:00,735 - root - INFO - 金额转换结果: 481.994409511640850529 -> 481994409511640850529
2025-06-14 22:06:03,067 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.802356799 Gwei
2025-06-14 22:06:03,070 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'dYdX (PoS)', 'symbol': 'DYDX', 'decimals': 18, 'chainId': 137}
2025-06-14 22:06:04,882 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:04,884 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:04,887 - root - WARNING - Polygon->ETH 桥接尝试 2 失败: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:04,888 - root - INFO - 等待 36 秒后重试...
2025-06-14 22:06:40,892 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-14 22:06:40,894 - root - INFO - 金额转换结果: 481.994409511640850529 -> 481994409511640850529
2025-06-14 22:06:41,867 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.758559599 Gwei
2025-06-14 22:06:41,870 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'dYdX (PoS)', 'symbol': 'DYDX', 'decimals': 18, 'chainId': 137}
2025-06-14 22:06:44,221 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:44,222 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:44,225 - trade_DYDX - ERROR - 桥接操作失败: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:44,226 - trade_DYDX - ERROR - 桥接操作失败: 余额不足，需要 481.99440951164087 DYDX，但只有 0.0 DYDX
2025-06-14 22:06:44,227 - trade_DYDX - INFO - 交易执行完成，耗时: 127.15秒
2025-06-14 22:06:44,228 - trade_DYDX - INFO - ================================================================================
2025-06-14 22:06:44,229 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DYDX_20250614_220644_new.json
2025-06-14 22:06:44,231 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DYDX_20250614_220644_new.json
2025-06-14 22:06:46,565 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-14 22:07:22,552 - arb_finder_main - INFO - 当前ETH gas价格: 0.72 Gwei
2025-06-14 22:07:22,553 - arb_finder_main - INFO - Gas价格较低 (0.72 Gwei)，开始执行套利任务
2025-06-14 22:17:41,812 - arb_finder_main - INFO - 用户中断操作
2025-06-15 10:17:24,845 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 10:17:24,850 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 10:17:26,790 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://ethereum-rpc.publicnode.com
2025-06-15 10:17:27,431 - token_change_tracker - ERROR - 从本地获取代币信息时出错: 'symbol'
2025-06-15 10:17:27,436 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 10:17:27,441 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 10:17:30,823 - token_change_tracker - INFO - 通过Web3获取代币信息: Smart Layer Network Token(SLN), 精度: 18
2025-06-15 10:17:30,823 - token_change_tracker - INFO - 交易最后接收的代币: 249.12103368248799112 SLN
2025-06-15 10:17:30,823 - trade_SLN - INFO - SLN: 从tx_token_change_tracker成功获取到代币数量: 249.12103368248799112
2025-06-15 10:17:30,824 - trade_SLN - INFO - 交易执行成功:
2025-06-15 10:17:30,824 - trade_SLN - INFO -   交易哈希: 0x88bea1fd2308d4fb5fe1fed6b55468e0b6321b31f8f81190bf83ba8c1db227ca
2025-06-15 10:17:30,824 - trade_SLN - INFO -   实际输出数量: 249.121033682488 SLN
2025-06-15 10:17:30,824 - trade_SLN - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-06-15 10:17:30,825 - trade_SLN - INFO - ================================================================================
2025-06-15 10:17:30,825 - trade_SLN - INFO - 开始执行 SLN 桥接操作 - 时间: 2025-06-15 10:17:30
2025-06-15 10:17:30,825 - trade_SLN - INFO - 桥接方向: ethereum_to_polygon
2025-06-15 10:17:30,825 - trade_SLN - INFO - 代币数量: 249.12103368248799112 SLN
2025-06-15 10:17:33,729 - trade_SLN - INFO - 从以太坊桥接到Polygon: 249.12103368248799112 SLN
2025-06-15 10:17:42,973 - root - INFO - 等待 900 秒后开始监控...
2025-06-15 10:32:42,983 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 10:32:42,990 - root - INFO - 代币精度: 18
2025-06-15 10:32:42,990 - root - INFO - 开始监控Polygon网络上的代币到账情况...
2025-06-15 10:32:42,990 - root - INFO - 接收地址: ******************************************
2025-06-15 10:32:42,990 - root - INFO - 以太坊代币地址: ******************************************
2025-06-15 10:32:42,990 - root - INFO - Polygon代币地址: ******************************************
2025-06-15 10:32:42,990 - root - INFO - 期望金额: 249.12103368248799112
2025-06-15 10:32:44,400 - root - INFO - WebSocket连接已建立，正在监控转账事件...
2025-06-15 10:34:22,623 - root - INFO - 检测到代币到账，金额匹配: 249.121033682488
2025-06-15 10:34:22,900 - trade_SLN - INFO - 桥接操作成功完成
2025-06-15 10:34:22,901 - trade_SLN - INFO - 桥接交易哈希: 0x1f17c1b6c150afc6370bf7d04f766e198963a7d064473c0b3899eecb45ab8f28
2025-06-15 10:34:22,901 - trade_SLN - INFO - Polygon交易哈希: None
2025-06-15 10:34:22,902 - trade_SLN - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\SLN_20250615_103422_bridge.json
2025-06-15 10:34:22,902 - trade_SLN - INFO - 桥接操作成功完成
2025-06-15 10:34:22,902 - trade_SLN - INFO - 交易执行完成，耗时: 1060.53秒
2025-06-15 10:34:22,903 - trade_SLN - INFO - ================================================================================
2025-06-15 10:34:22,903 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\SLN_20250615_103422_new.json
2025-06-15 10:34:22,904 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\SLN_20250615_103422_new.json
2025-06-15 13:21:06,225 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 13:21:06,230 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 13:21:12,777 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 13:21:14,517 - token_change_tracker - INFO - 成功连接到polygon RPC: https://polygon-mainnet.public.blastapi.io
2025-06-15 13:21:14,868 - token_change_tracker - INFO - 从本地获取到代币信息: INSUR(INSUR), 精度: 18
2025-06-15 13:21:14,868 - token_change_tracker - INFO - 交易最后接收的代币: 6860.824979711081332627 INSUR
2025-06-15 13:21:14,869 - trade_INSUR - INFO - INSUR: 从tx_token_change_tracker成功获取到代币数量: 6860.824979711081332627
2025-06-15 13:21:14,869 - trade_INSUR - INFO - 交易执行成功:
2025-06-15 13:21:14,869 - trade_INSUR - INFO -   交易哈希: 0x3544e2f62542701468334b621bfaf0a0d01ec0db84bca073374ece69ae2a4276
2025-06-15 13:21:14,869 - trade_INSUR - INFO -   实际输出数量: 6860.824979711081 INSUR
2025-06-15 13:21:14,869 - trade_INSUR - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-06-15 13:21:14,869 - trade_INSUR - INFO - ================================================================================
2025-06-15 13:21:14,869 - trade_INSUR - INFO - 开始执行 INSUR 桥接操作 - 时间: 2025-06-15 13:21:14
2025-06-15 13:21:14,869 - trade_INSUR - INFO - 桥接方向: polygon_to_ethereum
2025-06-15 13:21:14,869 - trade_INSUR - INFO - 代币数量: 6860.824979711081332627 INSUR
2025-06-15 13:21:18,867 - root - WARNING - Bridge初始化失败 (尝试 1/3): 无法连接到Polygon节点
2025-06-15 13:21:18,867 - root - INFO - 等待 8 秒后重试...
2025-06-15 13:21:29,471 - trade_INSUR - INFO - 从Polygon桥接到以太坊: 6860.824979711081332627 INSUR
2025-06-15 13:21:29,472 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:21:29,479 - root - INFO - 金额转换结果: 6860.824979711081332627 -> 6860824979711081332627
2025-06-15 13:21:30,213 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.358021779 Gwei
2025-06-15 13:21:30,215 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'InsurAce (PoS)', 'symbol': 'INSUR', 'decimals': 18, 'chainId': 137}
2025-06-15 13:21:30,826 - root - INFO - 开始从Polygon提取 6860.824979711081 INSUR 到以太坊
2025-06-15 13:21:31,782 - root - INFO - 估算gas限制: 47309，使用56770（含20%缓冲）
2025-06-15 13:21:32,114 - root - INFO - Gas费用信息:
2025-06-15 13:21:32,114 - root - INFO - Base fee: 3.7E-8 Gwei
2025-06-15 13:21:32,115 - root - INFO - Priority fee: 250 Gwei
2025-06-15 13:21:32,115 - root - INFO - Max fee: 250.000000074 Gwei
2025-06-15 13:21:37,724 - root - ERROR - 从Polygon转移到以太坊时出错: object AttributeDict can't be used in 'await' expression
2025-06-15 13:21:37,725 - root - WARNING - Polygon->ETH 桥接尝试 1 失败: object AttributeDict can't be used in 'await' expression
2025-06-15 13:21:37,725 - root - INFO - 等待 18 秒后重试...
2025-06-15 13:21:55,728 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:21:55,728 - root - INFO - 金额转换结果: 6860.824979711081332627 -> 6860824979711081332627
2025-06-15 13:21:56,103 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.327961884 Gwei
2025-06-15 13:21:56,105 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'InsurAce (PoS)', 'symbol': 'INSUR', 'decimals': 18, 'chainId': 137}
2025-06-15 13:21:56,748 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:21:56,750 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:21:56,753 - root - WARNING - Polygon->ETH 桥接尝试 2 失败: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:21:56,754 - root - INFO - 等待 36 秒后重试...
2025-06-15 13:22:32,762 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:22:32,762 - root - INFO - 金额转换结果: 6860.824979711081332627 -> 6860824979711081332627
2025-06-15 13:22:33,735 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.34252013 Gwei
2025-06-15 13:22:33,832 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'InsurAce (PoS)', 'symbol': 'INSUR', 'decimals': 18, 'chainId': 137}
2025-06-15 13:22:34,670 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:22:34,670 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:22:34,671 - trade_INSUR - ERROR - 桥接操作失败: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:22:34,671 - trade_INSUR - ERROR - 桥接操作失败: 余额不足，需要 6860.824979711081 INSUR，但只有 1.467965e-12 INSUR
2025-06-15 13:22:34,671 - trade_INSUR - INFO - 交易执行完成，耗时: 137.97秒
2025-06-15 13:22:34,671 - trade_INSUR - INFO - ================================================================================
2025-06-15 13:22:34,672 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\INSUR_20250615_132234_new.json
2025-06-15 13:22:34,672 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\INSUR_20250615_132234_new.json
2025-06-15 13:25:11,847 - trade_DMCC - INFO - ================================================================================
2025-06-15 13:25:11,849 - trade_DMCC - INFO - 开始执行 DMCC 买入交易 - 时间: 2025-06-15 13:25:11
2025-06-15 13:25:11,850 - trade_DMCC - INFO - 链: polygon, 投入金额: 8.0 USDT
2025-06-15 13:25:11,850 - trade_DMCC - INFO - 代币地址: ******************************************
2025-06-15 13:25:11,850 - trade_DMCC - INFO - DMCC: 将在polygon链上执行买入，代币地址: ******************************************
2025-06-15 13:25:11,851 - trade_DMCC - INFO - DMCC: 准备使用KyberSwap在polygon上执行8.0USDT买入DMCC交易
2025-06-15 13:25:41,906 - trade_DMCC - INFO - DMCC: 使用tx_token_change_tracker获取交易 0xed540afc21f127fd0cefdeefade753237ebd07436b334e083880aa92f32a1619 的代币数量...
2025-06-15 13:25:41,911 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 13:25:41,916 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 13:25:43,677 - token_change_tracker - INFO - 成功连接到polygon RPC: https://polygon-rpc.com
2025-06-15 13:25:44,015 - token_change_tracker - INFO - 从本地获取到代币信息: DMCC(DMCC), 精度: 18
2025-06-15 13:25:44,015 - token_change_tracker - INFO - 交易最后接收的代币: 436.874489498047735568 DMCC
2025-06-15 13:25:44,015 - trade_DMCC - INFO - DMCC: 从tx_token_change_tracker成功获取到代币数量: 436.874489498047735568
2025-06-15 13:25:44,015 - trade_DMCC - INFO - 交易执行成功:
2025-06-15 13:25:44,015 - trade_DMCC - INFO -   交易哈希: 0xed540afc21f127fd0cefdeefade753237ebd07436b334e083880aa92f32a1619
2025-06-15 13:25:44,015 - trade_DMCC - INFO -   实际输出数量: 436.87448949804775 DMCC
2025-06-15 13:25:44,016 - trade_DMCC - INFO - 开始执行桥接操作，方向: polygon_to_ethereum
2025-06-15 13:25:44,016 - trade_DMCC - INFO - ================================================================================
2025-06-15 13:25:44,016 - trade_DMCC - INFO - 开始执行 DMCC 桥接操作 - 时间: 2025-06-15 13:25:44
2025-06-15 13:25:44,016 - trade_DMCC - INFO - 桥接方向: polygon_to_ethereum
2025-06-15 13:25:44,016 - trade_DMCC - INFO - 代币数量: 436.874489498047735568 DMCC
2025-06-15 13:25:46,177 - trade_DMCC - INFO - 从Polygon桥接到以太坊: 436.874489498047735568 DMCC
2025-06-15 13:25:46,178 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:25:46,180 - root - INFO - 金额转换结果: 436.874489498047735568 -> 436874489498047735568
2025-06-15 13:25:46,535 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.361850824 Gwei
2025-06-15 13:25:46,538 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-15 13:25:47,145 - root - INFO - 开始从Polygon提取 436.87448949804775 DMCC 到以太坊
2025-06-15 13:25:48,046 - root - INFO - 估算gas限制: 47296，使用56755（含20%缓冲）
2025-06-15 13:25:48,343 - root - INFO - Gas费用信息:
2025-06-15 13:25:48,343 - root - INFO - Base fee: 4.1E-8 Gwei
2025-06-15 13:25:48,343 - root - INFO - Priority fee: 250 Gwei
2025-06-15 13:25:48,343 - root - INFO - Max fee: 250.000000082 Gwei
2025-06-15 13:25:54,294 - root - ERROR - 从Polygon转移到以太坊时出错: object AttributeDict can't be used in 'await' expression
2025-06-15 13:25:54,294 - root - WARNING - Polygon->ETH 桥接尝试 1 失败: object AttributeDict can't be used in 'await' expression
2025-06-15 13:25:54,294 - root - INFO - 等待 18 秒后重试...
2025-06-15 13:26:12,303 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:26:12,303 - root - INFO - 金额转换结果: 436.874489498047735568 -> 436874489498047735568
2025-06-15 13:26:13,241 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.36642521 Gwei
2025-06-15 13:26:13,242 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-15 13:26:13,829 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:13,829 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:13,830 - root - WARNING - Polygon->ETH 桥接尝试 2 失败: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:13,830 - root - INFO - 等待 36 秒后重试...
2025-06-15 13:26:49,843 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 13:26:49,843 - root - INFO - 金额转换结果: 436.874489498047735568 -> 436874489498047735568
2025-06-15 13:26:51,278 - src.bridge.pol_bridge.bridge_tokens - INFO - 当前以太坊gas价格: 0.361293694 Gwei
2025-06-15 13:26:51,279 - root - INFO - 代币信息: {'address': '******************************************', 'name': 'DMCCOIN(PoS)', 'symbol': 'DMCC', 'decimals': 18, 'chainId': 137}
2025-06-15 13:26:51,877 - src.bridge.pol_bridge.bridge_tokens - ERROR - 从Polygon提取代币失败: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:51,877 - root - ERROR - 从Polygon转移到以太坊时出错: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:51,878 - trade_DMCC - ERROR - 桥接操作失败: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:51,878 - trade_DMCC - ERROR - 桥接操作失败: 余额不足，需要 436.87448949804775 DMCC，但只有 0.0 DMCC
2025-06-15 13:26:51,878 - trade_DMCC - INFO - 交易执行完成，耗时: 100.03秒
2025-06-15 13:26:51,878 - trade_DMCC - INFO - ================================================================================
2025-06-15 13:26:51,879 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DMCC_20250615_132651_new.json
2025-06-15 13:26:51,879 - root - INFO - 交易记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\trade\DMCC_20250615_132651_new.json
2025-06-15 13:26:54,139 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 13:27:26,354 - arb_finder_main - INFO - 当前ETH gas价格: 0.31 Gwei
2025-06-15 13:27:26,354 - arb_finder_main - INFO - Gas价格较低 (0.31 Gwei)，开始执行套利任务
2025-06-15 13:34:13,413 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 13:34:45,591 - arb_finder_main - INFO - 当前ETH gas价格: 0.32 Gwei
2025-06-15 13:34:45,591 - arb_finder_main - INFO - Gas价格较低 (0.32 Gwei)，开始执行套利任务
2025-06-15 13:40:59,369 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 13:41:31,397 - arb_finder_main - INFO - 当前ETH gas价格: 0.35 Gwei
2025-06-15 13:41:31,397 - arb_finder_main - INFO - Gas价格较低 (0.35 Gwei)，开始执行套利任务
2025-06-15 13:47:12,186 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 13:47:44,811 - arb_finder_main - INFO - 当前ETH gas价格: 0.36 Gwei
2025-06-15 13:47:44,812 - arb_finder_main - INFO - Gas价格较低 (0.36 Gwei)，开始执行套利任务
2025-06-15 13:53:55,430 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 13:54:28,764 - arb_finder_main - INFO - 当前ETH gas价格: 0.35 Gwei
2025-06-15 13:54:28,764 - arb_finder_main - INFO - Gas价格较低 (0.35 Gwei)，开始执行套利任务
2025-06-15 14:02:42,304 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 14:02:49,319 - arb_finder_main - INFO - 用户中断操作
2025-06-15 21:26:11,525 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 21:26:11,529 - token_change_tracker - INFO - 配置文件加载成功
2025-06-15 21:26:15,464 - token_change_tracker - INFO - 成功连接到ethereum RPC: https://ethereum-rpc.publicnode.com
2025-06-15 21:26:17,562 - token_change_tracker - INFO - 从本地获取到代币信息: SDT(SDT), 精度: 18
2025-06-15 21:26:17,562 - token_change_tracker - INFO - 交易最后接收的代币: 1175.118089415329342961 SDT
2025-06-15 21:26:17,563 - trade_SDT - INFO - SDT: 从tx_token_change_tracker成功获取到代币数量: 1175.118089415329342961
2025-06-15 21:26:17,563 - trade_SDT - INFO - 交易执行成功:
2025-06-15 21:26:17,563 - trade_SDT - INFO -   交易哈希: 0x6ab3a842cb0eb3a217df57e0134c6beac008edefce7a649299e907ea584d5e40
2025-06-15 21:26:17,563 - trade_SDT - INFO -   实际输出数量: 1175.1180894153295 SDT
2025-06-15 21:26:17,564 - trade_SDT - INFO - 开始执行桥接操作，方向: ethereum_to_polygon
2025-06-15 21:26:17,564 - trade_SDT - INFO - ================================================================================
2025-06-15 21:26:17,564 - trade_SDT - INFO - 开始执行 SDT 桥接操作 - 时间: 2025-06-15 21:26:17
2025-06-15 21:26:17,564 - trade_SDT - INFO - 桥接方向: ethereum_to_polygon
2025-06-15 21:26:17,564 - trade_SDT - INFO - 代币数量: 1175.118089415329342961 SDT
2025-06-15 21:26:19,870 - trade_SDT - INFO - 从以太坊桥接到Polygon: 1175.118089415329342961 SDT
2025-06-15 21:26:26,768 - root - INFO - 等待 900 秒后开始监控...
2025-06-15 21:41:26,783 - root - INFO - 查询参数: symbol=None, address=******************************************, chain=polygon, chain_id=137
2025-06-15 21:41:26,789 - root - INFO - 代币精度: 18
2025-06-15 21:41:26,789 - root - INFO - 开始监控Polygon网络上的代币到账情况...
2025-06-15 21:41:26,789 - root - INFO - 接收地址: ******************************************
2025-06-15 21:41:26,789 - root - INFO - 以太坊代币地址: ******************************************
2025-06-15 21:41:26,789 - root - INFO - Polygon代币地址: ******************************************
2025-06-15 21:41:26,789 - root - INFO - 期望金额: 1175.118089415329342961
2025-06-15 21:41:29,594 - root - INFO - WebSocket连接已建立，正在监控转账事件...
2025-06-15 21:46:18,360 - root - INFO - 检测到代币到账，金额匹配: 1175.1180894153295
2025-06-15 21:46:18,670 - trade_SDT - INFO - 桥接操作成功完成
2025-06-15 21:46:18,671 - trade_SDT - INFO - 桥接交易哈希: 0xcc4b3d1214d6a089e44e5a149dac56f0a1d98115e25f4b18498fa94340483ab4
2025-06-15 21:46:18,671 - trade_SDT - INFO - Polygon交易哈希: None
2025-06-15 21:46:18,674 - trade_SDT - INFO - 桥接记录已保存: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\results\bridge\SDT_20250615_214618_bridge.json
2025-06-15 21:46:18,675 - trade_SDT - INFO - 桥接操作成功完成
2025-06-15 21:46:18,675 - trade_SDT - INFO - 开始执行卖出操作...
2025-06-15 21:46:18,676 - trade_SDT - INFO - ================================================================================
2025-06-15 21:46:18,677 - trade_SDT - INFO - 开始执行 SDT 卖出交易 - 时间: 2025-06-15 21:46:18
2025-06-15 21:46:21,455 - trade_SDT - ERROR - 执行卖出操作时出错: 必须提供私钥或私钥环境变量名
2025-06-15 21:46:21,460 - trade_SDT - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 730, in execute_sell
    bridge = PolygonEthereumBridge(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\bridge_tokens.py", line 127, in __init__
    raise ValueError("必须提供私钥或私钥环境变量名")
ValueError: 必须提供私钥或私钥环境变量名

2025-06-15 21:46:21,461 - trade_SDT - ERROR - 获取代币数量时出错: cannot access local variable 'target_token_address' where it is not associated with a value
2025-06-15 21:46:21,462 - trade_SDT - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 730, in execute_sell
    bridge = PolygonEthereumBridge(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\pol_bridge\bridge_tokens.py", line 127, in __init__
    raise ValueError("必须提供私钥或私钥环境变量名")
ValueError: 必须提供私钥或私钥环境变量名

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 473, in execute_buy
    sell_result = await self.execute_sell(actual_amount_str)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 899, in execute_sell
    'token_address': target_token_address,
                     ^^^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'target_token_address' where it is not associated with a value

2025-06-15 21:46:21,462 - root - ERROR - 执行交易时出错: 买入操作失败: 获取代币数量时出错: cannot access local variable 'target_token_address' where it is not associated with a value
2025-06-15 21:46:21,463 - root - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\scripts\arbitrage\portal_polygon_bridge\bridge_arb_executor.py", line 1003, in execute_trade_in_thread
    raise Exception(f"买入操作失败: {error_msg}")
Exception: 买入操作失败: 获取代币数量时出错: cannot access local variable 'target_token_address' where it is not associated with a value

2025-06-15 21:46:21,746 - arb_finder_main - INFO - 等待 30 秒后开始下一轮分析...
2025-06-15 21:46:55,605 - arb_finder_main - INFO - 当前ETH gas价格: 0.70 Gwei
2025-06-15 21:46:55,606 - arb_finder_main - INFO - Gas价格较低 (0.70 Gwei)，开始执行套利任务
