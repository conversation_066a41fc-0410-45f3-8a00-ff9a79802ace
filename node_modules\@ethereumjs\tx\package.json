{"name": "@ethereumjs/tx", "version": "5.4.0", "description": "Implementation of the various Ethereum Transaction Types", "keywords": ["ethereum", "transactions"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/tx#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+tx%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "mjbecze <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic"}], "type": "commonjs", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "DEBUG=ethjs npx vitest run --coverage.enabled --coverage.reporter=lcov", "docs:build": "typedoc --options typedoc.cjs", "examples": "tsx ../../scripts/examples-runner.ts -- tx", "examples:build": "npx embedme README.md", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "test": "npm run test:node", "test:browser": "npx vitest run --config=./vitest.config.browser.mts", "test:node": "npx vitest run", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@ethereumjs/common": "^4.4.0", "@ethereumjs/rlp": "^5.0.2", "@ethereumjs/util": "^9.1.0", "ethereum-cryptography": "^2.2.1"}, "devDependencies": {"@types/minimist": "^1.2.0", "@types/node-dir": "^0.0.34", "kzg-wasm": "^0.4.0", "minimist": "^1.2.0", "node-dir": "^0.1.16"}, "engines": {"node": ">=18"}}