#!/usr/bin/env -S npx tsx

/**
 * RPC整合示例
 * 演示如何使用项目的RPC配置进行CCIP跨链操作
 */

import { resolve } from 'node:path'
import { Providers } from '../src/providers.ts'
import { initializeRpcManager, DEFAULT_RPC_CONFIG_PATH } from '../src/config/rpc-integration.ts'
import { sendMessage } from '../src/commands/index.ts'

async function main() {
  console.log('🚀 CCIP RPC Integration Example')
  console.log('================================')

  try {
    // 1. 初始化RPC管理器
    console.log('\n📋 Loading RPC configuration...')
    const rpcManager = await initializeRpcManager(DEFAULT_RPC_CONFIG_PATH)
    
    // 2. 创建Providers实例，使用YAML配置
    console.log('\n🔗 Initializing providers with YAML config...')
    const providers = new Providers({ 'yaml-config': DEFAULT_RPC_CONFIG_PATH })
    
    // 3. 等待providers完成初始化
    console.log('\n⏳ Waiting for providers to initialize...')
    await providers.completed
    console.log('✅ Providers initialized successfully!')
    
    // 4. 显示可用的网络
    console.log('\n🌐 Available networks:')
    
    // 示例：获取以太坊主网的RPC端点
    const ethereumRpcs = rpcManager.getAvailableRpcsForChain(1)
    console.log(`Ethereum Mainnet (Chain ID: 1): ${ethereumRpcs.length} RPCs available`)
    
    // 示例：获取Polygon的RPC端点
    const polygonRpcs = rpcManager.getAvailableRpcsForChain(137)
    console.log(`Polygon Mainnet (Chain ID: 137): ${polygonRpcs.length} RPCs available`)
    
    // 示例：获取Avalanche测试网的RPC端点
    const fujiRpcs = rpcManager.getAvailableRpcsForChain(43113)
    console.log(`Avalanche Fuji Testnet (Chain ID: 43113): ${fujiRpcs.length} RPCs available`)
    
    // 5. 演示RPC轮换功能
    console.log('\n🔄 Demonstrating RPC rotation:')
    for (let i = 0; i < 3; i++) {
      const nextRpc = rpcManager.getNextRpcForChain(1)
      console.log(`  Next Ethereum RPC: ${nextRpc}`)
    }
    
    // 6. 演示失败处理
    console.log('\n❌ Demonstrating failure handling:')
    const testRpc = rpcManager.getNextRpcForChain(1)
    if (testRpc) {
      console.log(`  Marking RPC as failed: ${testRpc}`)
      rpcManager.markRpcAsFailed(testRpc)
      
      const nextRpc = rpcManager.getNextRpcForChain(1)
      console.log(`  Next available RPC: ${nextRpc}`)
    }
    
    console.log('\n✨ Example completed successfully!')
    
    // 清理
    providers.destroy()
    
  } catch (error) {
    console.error('❌ Error:', error)
    process.exit(1)
  }
}

// 示例：发送跨链消息的函数
async function sendCrossChainMessage() {
  console.log('\n📤 Cross-chain message example')
  console.log('==============================')
  
  try {
    // 使用YAML配置创建providers
    const providers = new Providers({ 'yaml-config': DEFAULT_RPC_CONFIG_PATH })
    
    // 等待初始化完成
    await providers.completed
    
    // 示例参数（请根据实际需求修改）
    const messageParams = {
      source: 43113, // Avalanche Fuji
      router: '0xF694E193200268f9a4868e4Aa017A0118C9a8177', // Fuji CCIP Router
      dest: 11155111, // Sepolia
      receiver: '0x0000000000000000000000000000000000000000', // 替换为实际接收地址
      data: '0x', // 空数据
      'transfer-tokens': [], // 无代币转移
      'fee-token': '0x0b9d5D9136855f6FEc3c0993feE6E9CE8a297846', // LINK token on Fuji
      'gas-limit': 200000,
      verbose: true,
      format: 'pretty' as const,
    }
    
    console.log('Message parameters:', messageParams)
    
    // 注意：这只是示例，实际使用时需要提供有效的参数
    // await sendMessage(providers, messageParams)
    
    console.log('✅ Message sending example completed')
    
    providers.destroy()
    
  } catch (error) {
    console.error('❌ Error sending message:', error)
  }
}

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { main, sendCrossChainMessage }
