import { CryptoCurrencyId } from "@ledgerhq/types-cryptoassets";
export declare const fetchTokensFromCDN: <T>(filename: string) => Promise<[T, string | undefined]>;
export type CALServiceOutput = {
    type: string;
    id: string;
    blockchain_name: string;
    network: string;
    network_family: string;
    chain_id: number;
    contract_address: string;
    token_identifier: string;
    decimals: number;
    delisted: boolean;
    network_type: string;
    name: string;
    symbol: string;
    ticker: string;
    units: string;
    live_signature: string;
    exchange_app_config_serialized: string;
    exchange_app_signature: string;
};
export declare const fetchTokensFromCALService: <T extends (keyof CALServiceOutput)[]>(chainDetails: {
    chainId?: string | number;
    standard?: string;
    blockchain_name?: CryptoCurrencyId;
}, output: T, etag?: string | null, next?: {
    cursor: string;
    tokens: Pick<CALServiceOutput, T[number]>[];
}) => Promise<{
    tokens: Pick<CALServiceOutput, T[number]>[];
    hash: string | undefined;
}>;
//# sourceMappingURL=index.d.ts.map