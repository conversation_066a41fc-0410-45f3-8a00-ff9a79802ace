import { resolve } from 'node:path'
import { readFile } from 'node:fs/promises'
import { parse } from 'yaml'

/**
 * RPC配置整合工具
 * 用于将项目的RPC配置整合到CCIP工具中
 */

// 网络名称到链ID的映射
export const NETWORK_CHAIN_ID_MAP: Record<string, number> = {
  // 主网
  'ethereum': 1,
  'polygon': 137,
  'bsc': 56,
  'avalanche': 43114,
  'arbitrum': 42161,
  'optimism': 10,
  'base': 8453,
  'astar': 592,
  'ronin': 2020,
  'ethereum-mainnet': 1,
  'polygon-mainnet': 137,
  'binance_smart_chain-mainnet': 56,
  'avalanche-mainnet': 43114,
  'ethereum-mainnet-arbitrum-1': 42161,
  'ethereum-mainnet-optimism-1': 10,
  'ethereum-mainnet-base-1': 8453,
  'polkadot-mainnet-astar': 592,
  'ronin-mainnet': 2020,
  
  // 测试网
  'ethereum-testnet-sepolia': 11155111,
  'polygon-testnet-amoy': 80002,
  'binance_smart_chain-testnet': 97,
  'avalanche-testnet-fuji': 43113,
  'ethereum-testnet-sepolia-arbitrum-1': 421614,
  'ethereum-testnet-sepolia-optimism-1': 11155420,
  'ethereum-testnet-sepolia-base-1': 84532,
}

// 链ID到网络名称的反向映射
export const CHAIN_ID_NETWORK_MAP: Record<number, string> = Object.fromEntries(
  Object.entries(NETWORK_CHAIN_ID_MAP).map(([name, id]) => [id, name])
)

interface RpcConfig {
  rpc_url?: string
  endpoint?: string
  backup_rpc_urls?: string[]
  backup_endpoints?: string[]
}

interface YamlConfig {
  rpc?: Record<string, RpcConfig>
}

/**
 * 从YAML配置文件加载RPC配置
 */
export async function loadRpcConfig(yamlPath: string): Promise<Map<number, string[]>> {
  try {
    const yamlContent = await readFile(yamlPath, 'utf8')
    const config: YamlConfig = parse(yamlContent)
    const chainRpcs = new Map<number, string[]>()
    
    if (config.rpc) {
      for (const [networkName, rpcConfig] of Object.entries(config.rpc)) {
        const chainId = NETWORK_CHAIN_ID_MAP[networkName]
        if (!chainId) {
          console.warn(`Unknown network: ${networkName}`)
          continue
        }
        
        const rpcs: string[] = []
        
        // 添加主要RPC端点
        if (rpcConfig.rpc_url) {
          rpcs.push(rpcConfig.rpc_url)
        }
        if (rpcConfig.endpoint) {
          rpcs.push(rpcConfig.endpoint)
        }
        
        // 添加备用RPC端点
        if (rpcConfig.backup_rpc_urls) {
          rpcs.push(...rpcConfig.backup_rpc_urls)
        }
        if (rpcConfig.backup_endpoints) {
          rpcs.push(...rpcConfig.backup_endpoints)
        }
        
        if (rpcs.length > 0) {
          chainRpcs.set(chainId, rpcs)
          console.log(`Loaded ${rpcs.length} RPC endpoints for ${networkName} (Chain ID: ${chainId})`)
        }
      }
    }
    
    console.log(`Total networks configured: ${chainRpcs.size}`)
    return chainRpcs
  } catch (error) {
    console.error(`Failed to load RPC config from ${yamlPath}:`, error)
    return new Map()
  }
}

/**
 * 获取指定链ID的RPC端点列表
 */
export function getRpcEndpointsForChain(chainRpcs: Map<number, string[]>, chainId: number): string[] {
  return chainRpcs.get(chainId) || []
}

/**
 * 获取所有RPC端点的扁平列表
 */
export function getAllRpcEndpoints(chainRpcs: Map<number, string[]>): string[] {
  const allRpcs: string[] = []
  for (const rpcs of chainRpcs.values()) {
    allRpcs.push(...rpcs)
  }
  return allRpcs
}

/**
 * 创建带有RPC轮换功能的Provider工厂
 */
export class RpcRotationManager {
  private chainRpcs: Map<number, string[]>
  private currentRpcIndex: Map<number, number> = new Map()
  private failedRpcs: Set<string> = new Set()
  private retryAfter: Map<string, number> = new Map()
  private readonly retryDelay = 60000 // 1分钟后重试失败的RPC

  constructor(chainRpcs: Map<number, string[]>) {
    this.chainRpcs = chainRpcs
  }

  /**
   * 获取指定链的下一个可用RPC端点
   */
  getNextRpcForChain(chainId: number): string | null {
    const rpcs = this.chainRpcs.get(chainId)
    if (!rpcs || rpcs.length === 0) {
      return null
    }

    const currentIndex = this.currentRpcIndex.get(chainId) || 0
    const now = Date.now()
    
    // 尝试找到一个可用的RPC
    for (let i = 0; i < rpcs.length; i++) {
      const index = (currentIndex + i) % rpcs.length
      const rpc = rpcs[index]
      
      // 检查RPC是否在重试延迟期内
      const retryTime = this.retryAfter.get(rpc)
      if (retryTime && now < retryTime) {
        continue
      }
      
      // 如果RPC之前失败过但已过重试时间，清除失败标记
      if (this.failedRpcs.has(rpc) && (!retryTime || now >= retryTime)) {
        this.failedRpcs.delete(rpc)
        this.retryAfter.delete(rpc)
      }
      
      // 更新当前索引到下一个RPC
      this.currentRpcIndex.set(chainId, (index + 1) % rpcs.length)
      return rpc
    }
    
    return null // 所有RPC都不可用
  }

  /**
   * 标记RPC为失败状态
   */
  markRpcAsFailed(rpc: string): void {
    this.failedRpcs.add(rpc)
    this.retryAfter.set(rpc, Date.now() + this.retryDelay)
    console.warn(`Marked RPC as failed: ${rpc}, will retry after ${this.retryDelay}ms`)
  }

  /**
   * 标记RPC为成功状态
   */
  markRpcAsSuccess(rpc: string): void {
    if (this.failedRpcs.has(rpc)) {
      this.failedRpcs.delete(rpc)
      this.retryAfter.delete(rpc)
      console.log(`RPC recovered: ${rpc}`)
    }
  }

  /**
   * 获取指定链的所有可用RPC端点
   */
  getAvailableRpcsForChain(chainId: number): string[] {
    const rpcs = this.chainRpcs.get(chainId) || []
    const now = Date.now()
    
    return rpcs.filter(rpc => {
      const retryTime = this.retryAfter.get(rpc)
      return !this.failedRpcs.has(rpc) || (retryTime && now >= retryTime)
    })
  }
}

/**
 * 默认配置文件路径
 */
export const DEFAULT_RPC_CONFIG_PATH = resolve(process.cwd(), '../../../config/rpc.yaml')

/**
 * 初始化RPC配置管理器
 */
export async function initializeRpcManager(configPath?: string): Promise<RpcRotationManager> {
  const yamlPath = configPath || DEFAULT_RPC_CONFIG_PATH
  const chainRpcs = await loadRpcConfig(yamlPath)
  return new RpcRotationManager(chainRpcs)
}
