# RPC Integration for CCIP Tools

本文档说明如何将项目的RPC配置整合到CCIP工具中，并实现RPC失败自动轮换功能。

## 功能特性

✅ **YAML配置支持**: 直接使用项目的 `config/rpc.yaml` 配置文件  
✅ **自动RPC轮换**: 当RPC失败时自动切换到备用RPC  
✅ **失败恢复**: 失败的RPC会在一定时间后重新尝试  
✅ **多网络支持**: 支持所有在配置文件中定义的网络  
✅ **向后兼容**: 保持与原有RPC配置方式的兼容性  

## 配置文件格式

CCIP工具现在支持读取项目的 `config/rpc.yaml` 文件，格式如下：

```yaml
rpc:
  ethereum:
    rpc_url: "https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY"
    backup_rpc_urls:
      - "https://mainnet.infura.io/v3/YOUR_KEY"
      - "https://rpc.ankr.com/eth"
  
  polygon:
    endpoint: "https://polygon-mainnet.g.alchemy.com/v2/YOUR_KEY"
    backup_endpoints:
      - "https://polygon-rpc.com"
      - "https://rpc.ankr.com/polygon"
  
  avalanche-testnet-fuji:
    rpc_url: "https://api.avax-test.network/ext/bc/C/rpc"
    backup_rpc_urls:
      - "https://rpc.ankr.com/avalanche_fuji"
```

## 使用方法

### 1. 命令行使用

```bash
# 使用YAML配置文件
./src/index.ts --yaml-config ../../../config/rpc.yaml show <tx_hash>

# 或者使用简化命令（默认路径）
./src/index.ts -y ../../../config/rpc.yaml send <params>

# 发送跨链消息示例
./src/index.ts send 43113 0xRouter 11155111 \
  --receiver 0xReceiver \
  --transfer-tokens 0xToken=1.0 \
  --yaml-config ../../../config/rpc.yaml
```

### 2. 编程方式使用

```typescript
import { Providers } from './src/providers.ts'
import { initializeRpcManager } from './src/config/rpc-integration.ts'

// 使用YAML配置创建Providers
const providers = new Providers({ 'yaml-config': '../../../config/rpc.yaml' })

// 或者使用RPC管理器
const rpcManager = await initializeRpcManager('../../../config/rpc.yaml')
const ethereumRpcs = rpcManager.getAvailableRpcsForChain(1)
```

### 3. RPC轮换管理

```typescript
import { RpcRotationManager } from './src/config/rpc-integration.ts'

// 获取下一个可用的RPC
const nextRpc = rpcManager.getNextRpcForChain(1) // Ethereum

// 标记RPC失败
rpcManager.markRpcAsFailed('https://failed-rpc.com')

// 标记RPC恢复
rpcManager.markRpcAsSuccess('https://recovered-rpc.com')

// 获取所有可用的RPC
const availableRpcs = rpcManager.getAvailableRpcsForChain(137) // Polygon
```

## 网络映射

支持的网络名称到链ID的映射：

| 网络名称 | 链ID | 描述 |
|---------|------|------|
| `ethereum` | 1 | 以太坊主网 |
| `polygon` | 137 | Polygon主网 |
| `bsc` | 56 | BSC主网 |
| `avalanche` | 43114 | Avalanche主网 |
| `arbitrum` | 42161 | Arbitrum主网 |
| `optimism` | 10 | Optimism主网 |
| `base` | 8453 | Base主网 |
| `ethereum-testnet-sepolia` | 11155111 | Sepolia测试网 |
| `avalanche-testnet-fuji` | 43113 | Fuji测试网 |

## 测试和验证

### 运行集成测试

```bash
# 测试RPC配置加载
npx tsx scripts/test-rpc-integration.ts

# 运行示例
npx tsx examples/rpc-integration-example.ts
```

### 验证配置

```bash
# 检查配置文件是否正确
./src/index.ts --yaml-config ../../../config/rpc.yaml getSupportedTokens 43113
```

## 故障排除

### 常见问题

1. **配置文件未找到**
   ```
   ❌ Config file not found: /path/to/config/rpc.yaml
   ```
   解决：确保配置文件路径正确，或使用绝对路径

2. **无可用RPC**
   ```
   ⚠️ No provider available for chain 1
   ```
   解决：检查配置文件中是否包含该网络的RPC配置

3. **RPC连接超时**
   ```
   ❌ Timeout connecting to https://rpc-url.com
   ```
   解决：RPC会自动轮换到备用端点，或检查网络连接

### 调试模式

```bash
# 启用详细日志
./src/index.ts --verbose --yaml-config ../../../config/rpc.yaml <command>
```

## 配置优先级

RPC配置的优先级顺序：

1. 命令行 `--rpcs` 参数
2. YAML配置文件 `--yaml-config`
3. 环境变量文件 `--rpcs-file`
4. 环境变量 `RPC_*`

## 性能优化

- **并发连接**: 同时连接多个RPC，选择最快响应的
- **失败缓存**: 失败的RPC会被暂时跳过，避免重复尝试
- **自动恢复**: 失败的RPC会在60秒后重新尝试
- **连接池**: 复用已建立的连接，减少延迟

## 安全注意事项

- 不要在配置文件中硬编码API密钥
- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 监控RPC使用量，避免超出限制

## 更新日志

- **v1.0.0**: 初始版本，支持YAML配置和RPC轮换
- **v1.0.1**: 添加失败恢复机制
- **v1.0.2**: 优化连接超时处理
