#!/usr/bin/env python3
"""
测试脚本：验证KyberSwapClient在真实交易时不使用代理IP
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.dex.KyberSwap.client import KyberSwapClient

async def test_proxy_behavior():
    """测试代理行为"""
    print("🧪 测试KyberSwapClient代理行为")

    # 测试参数
    chain = "polygon"
    token_in = "0x2791bca1f2de4661ed88a30c99a7a9449aa84174"  # USDC
    token_out = "0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270"  # WMATIC
    amount_in = "1000000"  # 1 USDC (6 decimals)

    try:
        # 1. 测试获取报价（应该使用代理）
        print("\n📊 测试1: 获取报价路由（应该使用代理）")
        client = KyberSwapClient(chain=chain)

        routes_data = await client.get_routes(
            token_in=token_in,
            token_out=token_out,
            amount_in=amount_in,
            slippage=0.5,
            is_native_in=False,
            save_gas=False,
            excluded_sources="bebop",
            use_proxy=True  # 明确指定使用代理
        )

        if "error" in routes_data:
            print(f"❌ 获取报价失败: {routes_data['error']}")
        else:
            print("✅ 获取报价成功（使用代理）")

        # 2. 测试真实交易路由（应该不使用代理）
        print("\n🔄 测试2: 真实交易路由（应该不使用代理）")

        routes_data_real = await client.get_routes(
            token_in=token_in,
            token_out=token_out,
            amount_in=amount_in,
            slippage=0.5,
            is_native_in=False,
            save_gas=False,
            excluded_sources="bebop",
            use_proxy=False  # 明确指定不使用代理
        )

        if "error" in routes_data_real:
            print(f"❌ 获取真实交易路由失败: {routes_data_real['error']}")
        else:
            print("✅ 获取真实交易路由成功（不使用代理）")

        # 3. 测试编码路由（应该不使用代理）
        if "error" not in routes_data:
            print("\n🔧 测试3: 编码路由（应该不使用代理）")

            # 模拟一个钱包地址
            test_address = "0xf9f2934331c051a37bb6e9c1397205c0e458f434"

            encoded_data = await client.encode_route(
                routes_data=routes_data,
                sender=test_address,
                recipient=test_address,
                slippage_tolerance=0.5,
                use_proxy=False  # 明确指定不使用代理
            )

            if "error" in encoded_data:
                print(f"❌ 编码路由失败: {encoded_data['error']}")
            else:
                print("✅ 编码路由成功（不使用代理）")

        # 4. 测试获取ETH价格（应该使用代理）
        print("\n💰 测试4: 获取ETH价格（应该使用代理）")
        eth_price = await client.get_eth_price_from_geckoterminal()
        print(f"✅ 获取ETH价格成功: ${eth_price}")

        print("\n🎉 所有测试完成！")
        print("📝 总结:")
        print("  - 获取报价路由: 使用代理 ✅")
        print("  - 真实交易路由: 不使用代理 ✅")
        print("  - 编码路由: 不使用代理 ✅")
        print("  - 获取价格: 使用代理 ✅")
        print("\n💡 重要改进:")
        print("  - 真实交易时只获取一次路由（避免重复调用）✅")
        print("  - 报价查询和真实交易使用不同的代理策略 ✅")

    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_proxy_behavior())
