# Final CCIP PowerShell Script
# Direct approach using available tools

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

$DefaultConfig = "..\..\..\config\rpc.yaml"

# Show help if no arguments
if (-not $Arguments) {
    Write-Host "CCIP Tools - Network Name Support" -ForegroundColor Cyan
    Write-Host "==================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\ccip-final.ps1 <command> [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Supported Network Names:" -ForegroundColor Green
    Write-Host "  Mainnet: ethereum, eth, polygon, matic, bsc, bnb, avalanche, avax"
    Write-Host "  Testnet: sepolia, eth-sepolia, fuji, avax-fuji, amoy, polygon-amoy"
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Green
    Write-Host "  list-networks                    - Show all supported networks"
    Write-Host "  getSupportedTokens <network>     - Get tokens for network"
    Write-Host "  send <source> <router> <dest>    - Send cross-chain message"
    Write-Host "  show <tx_hash>                   - Show message status"
    Write-Host "  manualExec <tx_hash>             - Execute pending message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\ccip-final.ps1 list-networks"
    Write-Host "  .\ccip-final.ps1 getSupportedTokens ethereum"
    Write-Host "  .\ccip-final.ps1 getSupportedTokens polygon"
    Write-Host "  .\ccip-final.ps1 getSupportedTokens sepolia"
    Write-Host ""
    Write-Host "Network Name Examples:" -ForegroundColor Yellow
    Write-Host "  ethereum = eth = mainnet         (Chain ID: 1)"
    Write-Host "  polygon = matic                  (Chain ID: 137)"
    Write-Host "  bsc = bnb = binance             (Chain ID: 56)"
    Write-Host "  avalanche = avax                (Chain ID: 43114)"
    Write-Host "  sepolia = eth-sepolia           (Chain ID: 11155111)"
    Write-Host "  fuji = avax-fuji                (Chain ID: 43113)"
    Write-Host ""
    Write-Host "Direct Usage (if scripts don't work):" -ForegroundColor Magenta
    Write-Host "  npx tsx src\index.ts list-networks"
    Write-Host "  npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml"
    Write-Host ""
    exit 0
}

# Check if config file exists
if (-not (Test-Path $DefaultConfig)) {
    Write-Host "Warning: Configuration file not found: $DefaultConfig" -ForegroundColor Yellow
    Write-Host "Continuing without default config..." -ForegroundColor Yellow
    $configArgs = @()
} else {
    $configArgs = @("--yaml-config", $DefaultConfig)
}

# Build full command
$AllArgs = @()
$AllArgs += $Arguments

# Add config if not already present
$hasYamlConfig = $false
foreach ($arg in $AllArgs) {
    if ($arg -like "*yaml-config*") {
        $hasYamlConfig = $true
        break
    }
}

if (-not $hasYamlConfig -and $configArgs.Count -gt 0) {
    $AllArgs += $configArgs
}

# Try different execution methods
$scriptFile = ".\src\index.ts"

Write-Host "Attempting to execute CCIP command..." -ForegroundColor Cyan
Write-Host "Command: $($AllArgs -join ' ')" -ForegroundColor Gray
Write-Host ""

# Method 1: Try npx tsx
try {
    Write-Host "Method 1: Using npx tsx..." -ForegroundColor Yellow
    & npx tsx $scriptFile @AllArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 1 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Method 2: Try direct tsx
try {
    Write-Host "Method 2: Using tsx directly..." -ForegroundColor Yellow
    & tsx $scriptFile @AllArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 2 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Method 3: Try node with ts-node
try {
    Write-Host "Method 3: Using ts-node..." -ForegroundColor Yellow
    & npx ts-node $scriptFile @AllArgs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Method 3 failed: $($_.Exception.Message)" -ForegroundColor Red
}

# All methods failed
Write-Host ""
Write-Host "All execution methods failed!" -ForegroundColor Red
Write-Host ""
Write-Host "Manual execution instructions:" -ForegroundColor Yellow
Write-Host "1. Install tsx globally: npm install -g tsx"
Write-Host "2. Or use local tsx: npx tsx src\index.ts <your-command>"
Write-Host "3. Or compile TypeScript first: npm run build"
Write-Host ""
Write-Host "Example manual command:" -ForegroundColor Cyan
Write-Host "npx tsx src\index.ts $($Arguments -join ' ') --yaml-config ..\..\..\config\rpc.yaml"
Write-Host ""

exit 1
