# CCIP Tools - Windows 快速开始指南

## 🚀 解决PowerShell执行问题

### 问题说明
在PowerShell中运行脚本时遇到的常见问题：
```
ccip.bat : 无法将"ccip.bat"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```

### 解决方案

#### 方法1: 使用 `.\` 前缀 (推荐)
```powershell
# 正确的PowerShell用法
.\ccip.bat list-networks
.\ccip.bat getSupportedTokens ethereum
.\ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
```

#### 方法2: 使用PowerShell脚本
```powershell
# 使用PowerShell脚本 (更好的错误处理)
.\ccip.ps1 list-networks
.\ccip.ps1 getSupportedTokens ethereum
.\ccip.ps1 send ethereum 0xRouter polygon --receiver 0xAddress
```

#### 方法3: 使用命令提示符 (CMD)
```cmd
# 在CMD中可以直接使用
ccip.bat list-networks
ccip.bat getSupportedTokens ethereum
ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
```

#### 方法4: 直接使用npx (高级用户)
```powershell
# 直接调用，适用于所有环境
npx tsx src\index.ts list-networks
npx tsx src\index.ts getSupportedTokens ethereum
npx tsx src\index.ts send ethereum 0xRouter polygon --receiver 0xAddress --yaml-config ..\..\..\config\rpc.yaml
```

## 📋 快速命令参考

### 查看支持的网络
```powershell
# PowerShell
.\ccip.bat list-networks

# 或者
.\ccip.ps1 list-networks

# 或者直接使用
npx tsx src\index.ts list-networks
```

### 查看支持的代币
```powershell
# 使用网络名称
.\ccip.bat getSupportedTokens ethereum
.\ccip.bat getSupportedTokens polygon
.\ccip.bat getSupportedTokens sepolia

# 使用链ID (仍然支持)
.\ccip.bat getSupportedTokens 1
.\ccip.bat getSupportedTokens 137
```

### 发送跨链消息
```powershell
# 基本消息
.\ccip.bat send ethereum 0xRouterAddress polygon --receiver 0xReceiverAddress

# 带数据的消息
.\ccip.bat send sepolia 0xRouterAddress fuji --receiver 0xReceiverAddress --data "Hello World"

# 代币转移
.\ccip.bat send ethereum 0xRouterAddress arbitrum --receiver 0xReceiverAddress --transfer-tokens 0xTokenAddress=1.5
```

### 查看消息状态
```powershell
.\ccip.bat show 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
```

### 手动执行消息
```powershell
.\ccip.bat manualExec 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
```

## 🌐 支持的网络名称

### 主网
- `ethereum`, `eth`, `mainnet` → Ethereum (Chain ID: 1)
- `polygon`, `matic` → Polygon (Chain ID: 137)
- `bsc`, `bnb`, `binance` → BSC (Chain ID: 56)
- `avalanche`, `avax` → Avalanche (Chain ID: 43114)
- `arbitrum`, `arb` → Arbitrum (Chain ID: 42161)
- `optimism`, `op` → Optimism (Chain ID: 10)
- `base` → Base (Chain ID: 8453)

### 测试网
- `sepolia`, `eth-sepolia` → Ethereum Sepolia (Chain ID: 11155111)
- `fuji`, `avax-fuji`, `avalanche-fuji` → Avalanche Fuji (Chain ID: 43113)
- `polygon-amoy`, `amoy`, `matic-amoy` → Polygon Amoy (Chain ID: 80002)
- `bsc-testnet`, `bnb-testnet` → BSC Testnet (Chain ID: 97)
- `arbitrum-sepolia`, `arb-sepolia` → Arbitrum Sepolia (Chain ID: 421614)
- `optimism-sepolia`, `op-sepolia` → Optimism Sepolia (Chain ID: 11155420)
- `base-sepolia` → Base Sepolia (Chain ID: 84532)

## 🛠️ 环境设置

### 1. 检查Node.js
```powershell
node --version
# 应该显示 v18.0.0 或更高版本
```

### 2. 安装依赖
```powershell
npm install
```

### 3. 验证配置文件
确保 `config\rpc.yaml` 文件存在于项目根目录：
```
C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\config\rpc.yaml
```

### 4. 测试安装
```powershell
# 测试基本功能
.\ccip.bat list-networks

# 如果上面失败，尝试
npx tsx src\index.ts list-networks
```

## ❌ 常见问题解决

### 问题1: PowerShell执行策略
```
无法加载文件，因为在此系统上禁止运行脚本
```

**解决方案**:
```powershell
# 临时设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process

# 然后运行脚本
.\ccip.ps1 list-networks
```

### 问题2: 找不到命令
```
无法将"ccip.bat"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```

**解决方案**:
```powershell
# 使用 .\ 前缀
.\ccip.bat list-networks

# 或者使用完整路径
C:\path\to\ccip-tools-ts\ccip.bat list-networks
```

### 问题3: 网络名称无效
```
❌ Invalid source network: "etherem"
💡 Did you mean: ethereum, ethereum-mainnet
```

**解决方案**:
```powershell
# 查看所有支持的网络
.\ccip.bat list-networks

# 使用正确的网络名称
.\ccip.bat getSupportedTokens ethereum
```

### 问题4: 配置文件未找到
```
❌ Configuration file not found: ..\..\..\config\rpc.yaml
```

**解决方案**:
1. 确保在正确的目录中运行命令
2. 检查配置文件是否存在
3. 使用绝对路径指定配置文件

## 🎯 推荐工作流程

### 1. 开发环境设置
```powershell
# 进入项目目录
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts

# 安装依赖
npm install

# 测试功能
.\ccip.bat list-networks
```

### 2. 日常使用
```powershell
# 查看网络
.\ccip.bat list-networks

# 查看代币
.\ccip.bat getSupportedTokens ethereum

# 发送消息
.\ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --data "test"
```

### 3. 调试模式
```powershell
# 启用详细日志
.\ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --verbose

# 直接使用npx进行调试
npx tsx src\index.ts send ethereum 0xRouter polygon --receiver 0xAddress --yaml-config ..\..\..\config\rpc.yaml --verbose
```

## 📚 更多帮助

```powershell
# 查看帮助
.\ccip.bat --help

# 查看特定命令帮助
.\ccip.bat send --help

# 查看版本
.\ccip.bat --version
```

## 💡 小贴士

1. **使用Tab补全**: 在PowerShell中输入 `.\ccip.` 然后按Tab键
2. **使用别名**: 创建PowerShell别名简化命令
3. **保存常用命令**: 将常用命令保存到文本文件中
4. **使用CMD**: 如果PowerShell有问题，可以切换到命令提示符(CMD)

现在您应该能够在Windows上正常使用CCIP工具了！
