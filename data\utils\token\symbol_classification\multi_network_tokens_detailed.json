{"ATH": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AthenaDAO Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Aetherium", "decimals": 8}], "WETH": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped Ether", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Wrapped Ether", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Wrapped Ether", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Wrapped Ether", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wrapped Ether", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wrapped Ether", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Wrapped Ether", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped Ether", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped Ether (Wormhole)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped Ether", "decimals": 18}], "USDT": [{"network": "arbitrum", "address": "******************************************", "name": "Tether USD", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Tether USD", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Tether USD", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "Tether USD", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "Tether USD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Tethers USDT Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "USDT (Wormhole)", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "Tether USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Tether USD", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Super USDT", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Tether USD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Tether USD", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Tether USD (Wormhole)", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "USDT (Wormhole)", "decimals": 6}], "ANIME": [{"network": "arbitrum", "address": "******************************************", "name": "Animecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Animecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Anime", "decimals": 18}], "BOOP": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "ETHFI": [{"network": "arbitrum", "address": "******************************************", "name": "ether.fi governance token", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "ether.fi governance token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "ether.fi governance token", "decimals": 18}], "NST": [{"network": "arbitrum", "address": "******************************************", "name": "Ninja Squad Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Ninja Squad Token", "decimals": 18}], "XAI": [{"network": "arbitrum", "address": "******************************************", "name": "Xai", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SideShift Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "XAI Corp", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Xspectra Ai", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "X.AI CORP", "decimals": 9}], "WBTC": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped BTC", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped BTC", "decimals": 8}, {"network": "optimism", "address": "******************************************", "name": "Wrapped BTC", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Wrapped BTC", "decimals": 8}], "USDC": [{"network": "arbitrum", "address": "******************************************", "name": "USD Coin", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "USD Coin (Arb1)", "decimals": null}, {"network": "base", "address": "******************************************", "name": "USD Coin", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "USD Coin", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "USD CLASSIC", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "USD Coin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "USD Coin", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "USD Coin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "USD Coin (PoS)", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "USD Coin", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "USD Coin (Wormhole)", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "USD Coin (Wormhole)", "decimals": null}], "YBR": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}], "PENDLE": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "SETH": [{"network": "arbitrum", "address": "******************************************", "name": "Sepolia ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Sepolia ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Synth sETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Sepolia ETH", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Synth sETH", "decimals": null}], "ARB": [{"network": "arbitrum", "address": "******************************************", "name": "Arbitrum", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Arbitrum", "decimals": 18}], "ANT": [{"network": "arbitrum", "address": "******************************************", "name": "Autonomi", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Aragon Network Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AntNetwork", "decimals": 18}], "ZRO": [{"network": "arbitrum", "address": "******************************************", "name": "LayerZero", "decimals": null}, {"network": "base", "address": "******************************************", "name": "LayerZero", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "LayerZero", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "LayerZero", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "LayerZero", "decimals": 18}], "MAGIC": [{"network": "arbitrum", "address": "******************************************", "name": "MAGIC", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "MAGICToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "MAGIC", "decimals": 18}], "LPT": [{"network": "arbitrum", "address": "******************************************", "name": "Livepeer Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Livepeer Token", "decimals": 18}], "DAI": [{"network": "arbitrum", "address": "******************************************", "name": "Dai Stablecoin", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Dai Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Dai Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Dai Stablecoin v1.0", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Dai Stablecoin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Dai Stablecoin", "decimals": 18}], "USD+": [{"network": "arbitrum", "address": "******************************************", "name": "USD+", "decimals": null}, {"network": "base", "address": "******************************************", "name": "USD+", "decimals": 6}, {"network": "optimism", "address": "******************************************", "name": "USD+", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "USD+", "decimals": 6}], "CRV": [{"network": "arbitrum", "address": "******************************************", "name": "Curve DAO Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Curve DAO Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Curve DAO Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Curve DAO Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "CRV (PoS)", "decimals": 18}], "GNS": [{"network": "arbitrum", "address": "******************************************", "name": "Gains Network", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Gains Network", "decimals": 18}], "EGP": [{"network": "arbitrum", "address": "******************************************", "name": "Eigenpie", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>ven <PERSON>", "decimals": 18}], "AAVE": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Aave (PoS)", "decimals": 18}], "HMX": [{"network": "arbitrum", "address": "******************************************", "name": "HMX", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "AXLUSDC": [{"network": "arbitrum", "address": "******************************************", "name": "Axelar Wrapped USDC", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Axelar Wrapped USDC", "decimals": 6}, {"network": "optimism", "address": "******************************************", "name": "Axelar Wrapped USDC", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Axelar Wrapped USDC", "decimals": 6}], "LINK": [{"network": "arbitrum", "address": "******************************************", "name": "ChainLink Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "ChainLink Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ChainLink Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "ChainLink Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "ChainLink Token", "decimals": 18}], "LDO": [{"network": "arbitrum", "address": "******************************************", "name": "Lido DAO Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Lido DAO Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Lido DAO Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Lido DAO Token (PoS)", "decimals": 18}], "PEAR": [{"network": "arbitrum", "address": "******************************************", "name": "Pear", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "PEAR AI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PEAR", "decimals": 18}], "TBTC": [{"network": "arbitrum", "address": "******************************************", "name": "Arbitrum tBTC v2", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Base tBTC v2", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "tBTC v2", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "tBTC", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Optimism tBTC v2", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Polygon tBTC v2", "decimals": 18}], "KIP": [{"network": "arbitrum", "address": "******************************************", "name": "KIP Protocol", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "KIP Protocol", "decimals": 18}], "AA": [{"network": "arbitrum", "address": "******************************************", "name": "Account Abstraction Incentive", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Addicted", "decimals": 18}], "ETH": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Arbitrum Ecosystem Thread Hash", "decimals": null}, {"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Etherium", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "World Computer", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Exodus", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Ethereumy", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "THE TICKER IS", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Viski.eth", "decimals": null}], "SPA": [{"network": "arbitrum", "address": "******************************************", "name": "Sperax", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Sperax", "decimals": 18}], "USDS": [{"network": "arbitrum", "address": "******************************************", "name": "Sperax USD", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "The Standard USD", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "USDS Stablecoin", "decimals": null}, {"network": "base", "address": "******************************************", "name": "USDS Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "USDS Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "United States Doge Service", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "United States Doge Service", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Spice USD", "decimals": 18}], "UNI": [{"network": "arbitrum", "address": "******************************************", "name": "Uniswap", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Uniswap", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Uniswap", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Uniswap (PoS)", "decimals": 18}], "USDE": [{"network": "arbitrum", "address": "******************************************", "name": "USDe", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "USDe", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "USDe", "decimals": null}], "WSTETH": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped liquid staked Ether 2.0 (PoS)", "decimals": 18}], "KUMA": [{"network": "arbitrum", "address": "******************************************", "name": "Kuma World", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Betakku<PERSON>", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "KUMA", "decimals": 8}], "RETH": [{"network": "arbitrum", "address": "******************************************", "name": "Rocket Pool ETH", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Rocket Pool ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Rocket Pool ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "StaFi", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Rocket Pool ETH", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Rocket Pool ETH (PoS)", "decimals": null}], "SPELL": [{"network": "arbitrum", "address": "******************************************", "name": "Spell Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Spell Token", "decimals": 18}], "CRVUSD": [{"network": "arbitrum", "address": "******************************************", "name": "Curve.Fi USD Stablecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Curve.Fi USD Stablecoin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Curve.Fi USD Stablecoin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Curve.Fi USD Stablecoin(PoS)", "decimals": 18}], "CAKE": [{"network": "arbitrum", "address": "******************************************", "name": "PancakeSwap Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "PancakeSwap Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "PancakeSwap Token", "decimals": 18}], "APEX": [{"network": "arbitrum", "address": "******************************************", "name": "ApeX Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Apex AI", "decimals": 18}], "SUSHI": [{"network": "arbitrum", "address": "******************************************", "name": "SushiToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SushiToken", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "SushiToken", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "SushiToken (PoS)", "decimals": 18}], "DMT": [{"network": "arbitrum", "address": "******************************************", "name": "DMT", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "DMT", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Dragon Master Token (PoS)", "decimals": 18}], "FRAX": [{"network": "arbitrum", "address": "******************************************", "name": "Frax", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Frax", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Frax", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Frax", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Frax (PoS)", "decimals": 18}], "RDNT": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "FOX": [{"network": "arbitrum", "address": "******************************************", "name": "FOX", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Foxify", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "FOX", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Fox", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Fox", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "FOX (PoS)", "decimals": 18}], "PEPE": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>i pepe", "decimals": null}, {"network": "base", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "base", "address": "******************************************", "name": "BasedPepe", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> by <PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> by <PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "PEPE", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "El Sapo Pepe", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Pepe Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Pepe by <PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "佩佩", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Proof of Pepe", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "PEPE THE DOG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> by 𝓙𝓪𝓼𝓸𝓷 𝓕𝓾𝓻𝓲𝓮", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "PEPEGOLD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Roaring Pepe", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Pepe Coin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "PEPE Optimism", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyPepe Token", "decimals": 18}], "MIM": [{"network": "arbitrum", "address": "******************************************", "name": "Magic Internet Money", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Magic Internet Money", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Magic Internet Money", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Magic Internet Money", "decimals": 18}], "EZETH": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>zo Restaked ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON>zo Restaked ETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON>zo Restaked ETH", "decimals": null}], "ANON": [{"network": "arbitrum", "address": "******************************************", "name": "HeyAnon", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Super Anon", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "HeyAnon", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "HeyAnon", "decimals": 18}], "GSWIFT": [{"network": "arbitrum", "address": "******************************************", "name": "GameSwift", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GameSwift", "decimals": 18}], "CBBTC": [{"network": "arbitrum", "address": "******************************************", "name": "Coinbase Wrapped BTC", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Coinbase Wrapped BTC", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Coinbase Wrapped BTC", "decimals": 8}], "GRT": [{"network": "arbitrum", "address": "******************************************", "name": "Graph <PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Graph <PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Graph <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Global Rental Token", "decimals": 8}], "@G": [{"network": "arbitrum", "address": "******************************************", "name": "G@ARB", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "G@ETH", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "G@POL", "decimals": 18}], "WEETH": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped eETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped eETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wrapped eETH", "decimals": null}], "UNIETH": [{"network": "arbitrum", "address": "******************************************", "name": "Universal ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Universal ETH", "decimals": 18}], "CARV": [{"network": "arbitrum", "address": "******************************************", "name": "CARV", "decimals": null}, {"network": "base", "address": "******************************************", "name": "CARV", "decimals": 18}], "SILO": [{"network": "arbitrum", "address": "******************************************", "name": "Silo Governance Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Silo Governance Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Silo Governance Token", "decimals": null}], "STG": [{"network": "arbitrum", "address": "******************************************", "name": "StargateToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "StargateToken", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "StargateToken", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "StargateToken", "decimals": 18}], "BONSAI": [{"network": "arbitrum", "address": "******************************************", "name": "Bonsai", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Bonsai <PERSON>", "decimals": 18}], "ORDER": [{"network": "arbitrum", "address": "******************************************", "name": "Orderly Network", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Orderly Network", "decimals": 18}], "OPUL": [{"network": "arbitrum", "address": "******************************************", "name": "OpulousToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "OpulousToken", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "OpulousToken", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "OpulousToken", "decimals": null}], "STAR": [{"network": "arbitrum", "address": "******************************************", "name": "StarHeroes", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "StarHeroes", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "STAR", "decimals": 18}], "RSETH": [{"network": "arbitrum", "address": "******************************************", "name": "KelpDao Restaked ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "rsETH", "decimals": 18}], "L3": [{"network": "arbitrum", "address": "******************************************", "name": "Layer3", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Layer3", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Layer3", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Layer3", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Layer3", "decimals": null}], "SOL": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped SOL", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped SOL (Wormhole)", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Smurfcat-o-Lantern", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wrapped SOL", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped SOL (Wormhole)", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "SOL", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "VEE": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "BLOCKv Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BLOCKv Token (PoS)", "decimals": 18}], "EMP": [{"network": "arbitrum", "address": "******************************************", "name": "Empyreal", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Empyreal", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Empyreal", "decimals": 18}], "DEFAI": [{"network": "arbitrum", "address": "******************************************", "name": "AISweatShop", "decimals": null}, {"network": "base", "address": "******************************************", "name": "DeFi Agents AI Token", "decimals": 18}], "VCNT": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": 18}], "GYD": [{"network": "arbitrum", "address": "******************************************", "name": "Gyro Dollar", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Gyro Dollar", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Gyro Dollar", "decimals": 18}], "BAL": [{"network": "arbitrum", "address": "******************************************", "name": "Balancer", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Balancer", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Balancer", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Balancer (PoS)", "decimals": 18}], "WOO": [{"network": "arbitrum", "address": "******************************************", "name": "Wootrade Network", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wootrade Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wootrade Network (PoS)", "decimals": 18}], "USX": [{"network": "arbitrum", "address": "******************************************", "name": "dForce USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "dForce USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "dForce USD", "decimals": null}], "VRTX": [{"network": "arbitrum", "address": "******************************************", "name": "Vertex", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "VORTEX", "decimals": 18}], "HYPER": [{"network": "arbitrum", "address": "******************************************", "name": "Hyperlane", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Hyperlane", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Hyper", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Hyperlane", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Hyperlane", "decimals": null}], "OBT": [{"network": "arbitrum", "address": "******************************************", "name": "Orbiter Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Orbiter Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Oobit", "decimals": 18}], "AIUS": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "MAI": [{"network": "arbitrum", "address": "******************************************", "name": "Mai Stablecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Micro AI", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Mai Stablecoin", "decimals": null}], "APE": [{"network": "arbitrum", "address": "******************************************", "name": "ApeCoin", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "ApeCoin", "decimals": null}, {"network": "base", "address": "******************************************", "name": "ApeStore", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ApeCoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "American Pepe", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Ape", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Ape coin", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "BTC.B": [{"network": "arbitrum", "address": "******************************************", "name": "Bitcoin", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Bitcoin", "decimals": null}], "AXGT": [{"network": "arbitrum", "address": "******************************************", "name": "AxonDAO Governance Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "AxonDAO Governance Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AxonDAO Governance Token", "decimals": 18}], "GHO": [{"network": "arbitrum", "address": "******************************************", "name": "Gho Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Gho Token", "decimals": 18}], "PREMIA": [{"network": "arbitrum", "address": "******************************************", "name": "Premia", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Premia", "decimals": 18}], "OVER": [{"network": "arbitrum", "address": "******************************************", "name": "Overtime DAO Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Overtime DAO Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Overtime DAO Token", "decimals": null}], "FRXETH": [{"network": "arbitrum", "address": "******************************************", "name": "Fr<PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Fr<PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Fr<PERSON>", "decimals": null}], "WSG": [{"network": "arbitrum", "address": "******************************************", "name": "Wall Street Games", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wall Street Games", "decimals": 18}], "W": [{"network": "arbitrum", "address": "******************************************", "name": "Wormhole Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Wormhole Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wormhole Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wormhole Token", "decimals": null}], "EURC": [{"network": "arbitrum", "address": "******************************************", "name": "EURC", "decimals": null}, {"network": "base", "address": "******************************************", "name": "EURC", "decimals": 6}], "BONK": [{"network": "arbitrum", "address": "******************************************", "name": "Bonk", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Bonk", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bonk", "decimals": 5}, {"network": "eth", "address": "******************************************", "name": "Bonk on ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BONKTOKEN", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Bonk", "decimals": 5}], "THALES": [{"network": "arbitrum", "address": "******************************************", "name": "Thales DAO Token", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Optimistic Thales Token", "decimals": null}], "SWORLD": [{"network": "arbitrum", "address": "******************************************", "name": "Seedworld", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Seedworld", "decimals": 18}], "ALUSD": [{"network": "arbitrum", "address": "******************************************", "name": "Alchemix USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Alchemix USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Alchemix USD", "decimals": null}], "REKT": [{"network": "arbitrum", "address": "******************************************", "name": "REKT", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Rekt", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Rekt", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "REKT", "decimals": 18}], "USDX": [{"network": "arbitrum", "address": "******************************************", "name": "Synthetix USD", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "USDX", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Whiterock USD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "USDx", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Dollars", "decimals": 9}], "LNDX": [{"network": "arbitrum", "address": "******************************************", "name": "LandX Governance Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LandX Governance Token", "decimals": 6}], "SMT": [{"network": "arbitrum", "address": "******************************************", "name": "SMT", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Swarm Markets", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SMARTMALL TOKEN", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Swarm Markets", "decimals": 18}], "WNT": [{"network": "arbitrum", "address": "******************************************", "name": "Wicrypt Network Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "WenToken", "decimals": 18}], "FLY": [{"network": "arbitrum", "address": "******************************************", "name": "Fluidity", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 4}], "XVS": [{"network": "arbitrum", "address": "******************************************", "name": "Venus XVS", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Venus XVS", "decimals": 18}], "TRUF": [{"network": "arbitrum", "address": "******************************************", "name": "Truflation", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Truflation", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Truflation", "decimals": 18}], "GS": [{"network": "arbitrum", "address": "******************************************", "name": "GammaSwap", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GoldStar", "decimals": 9}], "GOHM": [{"network": "arbitrum", "address": "******************************************", "name": "Governance OHM", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Governance OHM", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Governance OHM", "decimals": 18}], "PEAS": [{"network": "arbitrum", "address": "******************************************", "name": "Peapods", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Peapods", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Peapods", "decimals": 18}], "OHM": [{"network": "arbitrum", "address": "******************************************", "name": "Olympus", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Olympus", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Olympus", "decimals": 9}], "KNC": [{"network": "arbitrum", "address": "******************************************", "name": "Kyber Network Crystal v2", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Kyber Network Crystal v2", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Kyber Network Crystal", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Kyber Network Crystal v2", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Kyber Network Crystal v2 (PoS)", "decimals": 18}], "SMOL": [{"network": "arbitrum", "address": "******************************************", "name": "Smolcoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SMOL", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "smol", "decimals": 9}], "COW": [{"network": "arbitrum", "address": "******************************************", "name": "CoW Protocol Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "CoW Protocol Token", "decimals": 18}], "CBETH": [{"network": "arbitrum", "address": "******************************************", "name": "Coinbase Wrapped Staked ETH", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Coinbase Wrapped Staked ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Coinbase Wrapped Staked ETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Coinbase Wrapped Staked ETH", "decimals": null}], "MOLTEN": [{"network": "arbitrum", "address": "******************************************", "name": "Molten", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Molten", "decimals": null}], "USD0": [{"network": "arbitrum", "address": "******************************************", "name": "Usual USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Usual USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "USD0", "decimals": null}], "AGEUR": [{"network": "arbitrum", "address": "******************************************", "name": "agEUR", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "agEUR", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "agEUR", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "agEUR", "decimals": 18}], "SFUND": [{"network": "arbitrum", "address": "******************************************", "name": "SeedifyFund", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SeedifyFund", "decimals": 18}], "JOE": [{"network": "arbitrum", "address": "******************************************", "name": "JoeToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "POGAI": [{"network": "arbitrum", "address": "******************************************", "name": "poor guy", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "POGAI", "decimals": 9}], "COMP": [{"network": "arbitrum", "address": "******************************************", "name": "Compound", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Compound", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Compound", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Compound", "decimals": 18}], "EVA": [{"network": "arbitrum", "address": "******************************************", "name": "EverValueCoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "eVa-ai", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Evanesco Network", "decimals": 18}], "KRL": [{"network": "arbitrum", "address": "******************************************", "name": "Kryll", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Kryll", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Kryll", "decimals": null}], "SNSY": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "HOLD": [{"network": "arbitrum", "address": "******************************************", "name": "HOLD TOKEN", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Everybody", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}], "ZUNETH": [{"network": "arbitrum", "address": "******************************************", "name": "Zunami ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Zunami ETH", "decimals": 18}], "LOCK": [{"network": "arbitrum", "address": "******************************************", "name": "Lockquidity", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Meridian Network", "decimals": 18}], "HOP": [{"network": "arbitrum", "address": "******************************************", "name": "Hop", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Hop", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Hop", "decimals": null}], "USOL": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>ana (Universal)", "decimals": null}, {"network": "base", "address": "******************************************", "name": "<PERSON>ana (Universal)", "decimals": 18}], "SYN": [{"network": "arbitrum", "address": "******************************************", "name": "Synapse", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Synapse", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Synapse", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Synapse", "decimals": null}], "GRAI": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": null}], "RBC": [{"network": "arbitrum", "address": "******************************************", "name": "RUBIC TOKEN", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "RUBIC TOKEN", "decimals": 18}], "RSR": [{"network": "arbitrum", "address": "******************************************", "name": "Reserve Rights", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Reserve Rights", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Reserve Rights", "decimals": 18}], "TANGO": [{"network": "arbitrum", "address": "******************************************", "name": "Contango", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "keyTango Token", "decimals": 18}], "GOLD": [{"network": "arbitrum", "address": "******************************************", "name": "GoldenBoys", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GOLD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GoldenBoys", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Goldcoin", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Gold", "decimals": 9}], "UXRP": [{"network": "arbitrum", "address": "******************************************", "name": "XRP (Universal)", "decimals": null}, {"network": "base", "address": "******************************************", "name": "XRP (Universal)", "decimals": 18}], "MXNB": [{"network": "arbitrum", "address": "******************************************", "name": "MXNB", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "MXNB", "decimals": 6}], "OSAK": [{"network": "arbitrum", "address": "******************************************", "name": "Osaka Protocol", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Osaka Protocol", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Osaka Protocol", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Osaka Protocol", "decimals": 18}], "XYRO": [{"network": "arbitrum", "address": "******************************************", "name": "XYRO", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "XYRO", "decimals": 18}], "ICHI": [{"network": "arbitrum", "address": "******************************************", "name": "ICHI", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "ICHI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ichi.farm", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "ICHI", "decimals": 18}], "TAROT": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "QKNTL": [{"network": "arbitrum", "address": "******************************************", "name": "Quick Intel", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Quick Intel", "decimals": 18}], "FXS": [{"network": "arbitrum", "address": "******************************************", "name": "Frax Share", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Frax Share", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Frax Share", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Frax Share", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Frax Share", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Frax Share (PoS)", "decimals": 18}], "OMNI": [{"network": "arbitrum", "address": "******************************************", "name": "OmniCat", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "OmniCat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Omni Network", "decimals": 18}], "USDD": [{"network": "arbitrum", "address": "******************************************", "name": "Decentralized USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Decentralized USD", "decimals": 18}], "AURA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>ra", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Au<PERSON>", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "<PERSON>ra", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>ra", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>ra", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "<PERSON>ra", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>ra", "decimals": 18}], "DEUS": [{"network": "arbitrum", "address": "******************************************", "name": "DEUS", "decimals": null}, {"network": "base", "address": "******************************************", "name": "DEUS", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DEUS", "decimals": 18}], "DPX": [{"network": "arbitrum", "address": "******************************************", "name": "Dopex Governance Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Dopex Governance Token", "decimals": 18}], "NYA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "MONEY": [{"network": "arbitrum", "address": "******************************************", "name": "Defi.Money Stablecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "MoneyFund", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Defi.Money Stablecoin", "decimals": null}], "FBOMB": [{"network": "arbitrum", "address": "******************************************", "name": "Fantom Bomb", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Fantom Bomb", "decimals": null}], "ADOGE": [{"network": "arbitrum", "address": "******************************************", "name": "ArbiDoge", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "American Dogecoin", "decimals": 9}], "XSPACE": [{"network": "arbitrum", "address": "******************************************", "name": "xSpace Token", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "xSpace Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "xSpace Token", "decimals": null}], "DONUT": [{"network": "arbitrum", "address": "******************************************", "name": "Donut", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Donut", "decimals": 18}], "ATA": [{"network": "arbitrum", "address": "******************************************", "name": "ATLAS", "decimals": null}, {"network": "base", "address": "******************************************", "name": "ATA", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Automata", "decimals": 18}], "PX": [{"network": "arbitrum", "address": "******************************************", "name": "Phoenix Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Pyro Xcoin", "decimals": 18}], "ARA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "USD0++": [{"network": "arbitrum", "address": "******************************************", "name": "USD0 Liquid Bond", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "USD0 Liquid Bond", "decimals": 18}], "SOLVBTC": [{"network": "arbitrum", "address": "******************************************", "name": "Solv BTC", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Solv BTC", "decimals": 18}], "ARC": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "ARC", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Arcade Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "NYAN": [{"network": "arbitrum", "address": "******************************************", "name": "ArbiNYAN", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "ALETH": [{"network": "arbitrum", "address": "******************************************", "name": "Alchemix ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Alchemix ETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Alchemix ETH", "decimals": null}], "LUSD": [{"network": "arbitrum", "address": "******************************************", "name": "LUSD Stablecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LUSD Stablecoin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "LUSD Stablecoin", "decimals": null}], "USDGLO": [{"network": "arbitrum", "address": "******************************************", "name": "Glo Dollar", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Glo Dollar", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Glo Dollar", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Glo Dollar", "decimals": 18}], "LDY": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "PEN": [{"network": "arbitrum", "address": "******************************************", "name": "Pentagon", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "PEN", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Pentagon", "decimals": 18}], "LSETH": [{"network": "arbitrum", "address": "******************************************", "name": "Liquid Staked ETH", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Liquid Staked ETH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Liquid Staked ETH", "decimals": 18}], "LADYS": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "BRUH": [{"network": "arbitrum", "address": "******************************************", "name": "BRUH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Bruh", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "The News, Bruh", "decimals": null}], "CRX": [{"network": "arbitrum", "address": "******************************************", "name": "CORTEX", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Cerebro", "decimals": 18}], "TST": [{"network": "arbitrum", "address": "******************************************", "name": "Standard Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Standard Token", "decimals": 18}], "USH": [{"network": "arbitrum", "address": "******************************************", "name": "unshETHing_Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "unshETHing_Token", "decimals": 18}], "OX": [{"network": "arbitrum", "address": "******************************************", "name": "OX Coin", "decimals": null}, {"network": "base", "address": "******************************************", "name": "OX Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "OX Coin", "decimals": 18}], "MOON": [{"network": "arbitrum", "address": "******************************************", "name": "Moons", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Black Unicorn Corp.", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MoonToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Moonflow", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Polywolf", "decimals": 18}], "CHR": [{"network": "arbitrum", "address": "******************************************", "name": "CHRONOS", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Chroma", "decimals": 6}], "OSETH": [{"network": "arbitrum", "address": "******************************************", "name": "Staked ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Staked ETH", "decimals": 18}], "FTW": [{"network": "arbitrum", "address": "******************************************", "name": "Black Agnus", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Black Agnus", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Black Agnus", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Black Agnus", "decimals": 18}], "NCASH": [{"network": "arbitrum", "address": "******************************************", "name": "Nutcash", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Nutcash", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "NucleusVision", "decimals": 18}], "EPENDLE": [{"network": "arbitrum", "address": "******************************************", "name": "Equilibria Pendle", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Equilibria Pendle", "decimals": 18}], "GOA": [{"network": "arbitrum", "address": "******************************************", "name": "GOAT", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Gulf of America", "decimals": 9}], "RPL": [{"network": "arbitrum", "address": "******************************************", "name": "Rocket Pool Protocol", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Rocket Pool Protocol", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Rocket Pool", "decimals": 18}], "SFRXETH": [{"network": "arbitrum", "address": "******************************************", "name": "Staked Frax Ether", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Staked Frax Ether", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Staked Frax Ether", "decimals": null}], "SDEX": [{"network": "arbitrum", "address": "******************************************", "name": "SmarDex Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SmarDex Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SmarDex Token", "decimals": null}], "POOL": [{"network": "arbitrum", "address": "******************************************", "name": "PoolTogether", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "PoolTogether", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "PoolTogether", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "PoolTogether (PoS)", "decimals": 18}], "ELK": [{"network": "arbitrum", "address": "******************************************", "name": "Elk", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Elk", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Elk", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Elk", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Elk", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Elk", "decimals": null}], "AXLKNC": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON> Wrapped KNC", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON> Wrapped KNC", "decimals": null}], "FFM": [{"network": "arbitrum", "address": "******************************************", "name": "Florence Finance Medici", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Florence Finance Medici", "decimals": 18}], "SECT": [{"network": "arbitrum", "address": "******************************************", "name": "Sector", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SECT BOT", "decimals": 18}], "DF": [{"network": "arbitrum", "address": "******************************************", "name": "dForce", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "dForce", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "dForce", "decimals": null}], "UNIBTC": [{"network": "arbitrum", "address": "******************************************", "name": "uniBTC", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "uniBTC", "decimals": 8}, {"network": "optimism", "address": "******************************************", "name": "uniBTC", "decimals": null}], "O3": [{"network": "arbitrum", "address": "******************************************", "name": "O3 Swap Token", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "O3", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "O3 Swap Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "O3 Swap Token", "decimals": 18}], "DOG": [{"network": "arbitrum", "address": "******************************************", "name": "The Doge NFT", "decimals": null}, {"network": "base", "address": "******************************************", "name": "The Doge NFT", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "The Doge NFT", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "LEDOG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DOG", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "The Doge NFT", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DOG•GO•TO•THE•MOON", "decimals": 5}], "EMAX": [{"network": "arbitrum", "address": "******************************************", "name": "EthereumMax", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "EthereumMax", "decimals": 18}], "GOVI": [{"network": "arbitrum", "address": "******************************************", "name": "GOVI", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GOVI", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GOVI (PoS)", "decimals": null}], "VOLTA": [{"network": "arbitrum", "address": "******************************************", "name": "Volta Protocol Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Volta Club", "decimals": 18}], "BADGER": [{"network": "arbitrum", "address": "******************************************", "name": "Badger", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Badger", "decimals": 18}], "WTAO": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped TAO", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped TAO", "decimals": 9}], "AGI": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "AGI Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Agility", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Artificial Gork Intelligence", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Artificial Grok Intelligence", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Artificial General Intelligence", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Artificial Grok Intelligence", "decimals": 9}], "AMKT": [{"network": "arbitrum", "address": "******************************************", "name": "Alongside Crypto Market Index", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Alongside Crypto Market Index (PoS)", "decimals": 18}], "USDV": [{"network": "arbitrum", "address": "******************************************", "name": "USDV", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "USDV", "decimals": null}], "BOBRCRV": [{"network": "arbitrum", "address": "******************************************", "name": "CURVEBOBR", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "CURVEBOBR", "decimals": 18}], "ASTGO": [{"network": "arbitrum", "address": "******************************************", "name": "Astrinea Gold", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Astrinea Gold", "decimals": null}], "CNFI": [{"network": "arbitrum", "address": "******************************************", "name": "Connect Financial", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Connect Financial", "decimals": 18}], "MAV": [{"network": "arbitrum", "address": "******************************************", "name": "Maver<PERSON>", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Maver<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Maver<PERSON>", "decimals": 18}], "AI": [{"network": "arbitrum", "address": "******************************************", "name": "Any Inu", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "Flourishing AI Token", "decimals": null}, {"network": "base", "address": "******************************************", "name": "AI Protocol", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Any Inu", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AI PIN", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AiDoge", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Ai.com", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "ALL IN", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "AI Challenges By <PERSON><PERSON>", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "Any Inu", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Flourishing AI Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Any Inu", "decimals": null}], "GTC": [{"network": "arbitrum", "address": "******************************************", "name": "Gitcoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Gitcoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Game.com Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Gitcoin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "GTC", "decimals": null}], "FLASH": [{"network": "arbitrum", "address": "******************************************", "name": "Flashstake", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "FlashToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FLASH", "decimals": 18}], "SIS": [{"network": "arbitrum", "address": "******************************************", "name": "Symbiosis", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Symbiosis", "decimals": 18}], "L2DAO": [{"network": "arbitrum", "address": "******************************************", "name": "Layer2DAO", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Layer2DAO", "decimals": null}], "RPK": [{"network": "arbitrum", "address": "******************************************", "name": "RepubliK", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "RepubliK", "decimals": 18}], "NUT": [{"network": "arbitrum", "address": "******************************************", "name": "Nutcoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Nutcoin", "decimals": 18}], "DOLA": [{"network": "arbitrum", "address": "******************************************", "name": "Dola USD Stablecoin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Dola USD Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Dola USD Stablecoin", "decimals": null}], "PBX": [{"network": "arbitrum", "address": "******************************************", "name": "Paribus", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Paribus", "decimals": 18}], "STR": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Stater (PoS)", "decimals": 18}], "LQTY": [{"network": "arbitrum", "address": "******************************************", "name": "LQTY", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LQTY", "decimals": 18}], "BIFI": [{"network": "arbitrum", "address": "******************************************", "name": "beefy.finance", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Beefy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BiFi", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "beefy.finance", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "beefy.finance", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "beefy.finance", "decimals": 18}], "FISH": [{"network": "arbitrum", "address": "******************************************", "name": "SwapFish", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "FishToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Fish", "decimals": 18}], "OPEN": [{"network": "arbitrum", "address": "******************************************", "name": "OpenWorld", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Open Stablecoin Index", "decimals": 18}], "KIBSHI": [{"network": "arbitrum", "address": "******************************************", "name": "KiboShib", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "KiboShib", "decimals": 18}], "MOR": [{"network": "arbitrum", "address": "******************************************", "name": "MOR", "decimals": null}, {"network": "base", "address": "******************************************", "name": "MOR", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MOR", "decimals": 18}], "LOTUS": [{"network": "arbitrum", "address": "******************************************", "name": "White Lotus", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LOTUS", "decimals": 18}], "DERI": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "IBEX": [{"network": "arbitrum", "address": "******************************************", "name": "Impermax", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Impermax (PoS)", "decimals": 18}], "DRV": [{"network": "arbitrum", "address": "******************************************", "name": "Derive", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Derive", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Derive", "decimals": 18}], "WBAN": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped <PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped <PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped <PERSON><PERSON>", "decimals": 18}], "WBGL": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped BGL", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped BGL", "decimals": 18}], "WUSDM": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped Mountain Protocol USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped Mountain Protocol USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Wrapped Mountain Protocol USD", "decimals": null}], "AUSD": [{"network": "arbitrum", "address": "******************************************", "name": "AUSD Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "AUSD", "decimals": 6}], "AXL": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "TRT": [{"network": "arbitrum", "address": "******************************************", "name": "TRUST AI", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Testosterone Replacement Therapy", "decimals": 18}], "OLAS": [{"network": "arbitrum", "address": "******************************************", "name": "Autonolas", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Autonolas", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Autonolas", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Autonolas(PoS)", "decimals": 18}], "RED": [{"network": "arbitrum", "address": "******************************************", "name": "Redacted", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Redstone", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "REDKoin", "decimals": null}], "DFX": [{"network": "arbitrum", "address": "******************************************", "name": "DFX Token (L2)", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "DFX Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DFX Token (L2)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DFX Token (PoS)", "decimals": 18}], "HEGIC": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "WBNB": [{"network": "arbitrum", "address": "******************************************", "name": "Wrapped BNB", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Wrapped BNB (Wormhole)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped BNB (Wormhole)", "decimals": 18}], "AEG": [{"network": "arbitrum", "address": "******************************************", "name": "Aegis token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Aether Games", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "AETHER GAMES", "decimals": 18}], "LYRA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "DZHV": [{"network": "arbitrum", "address": "******************************************", "name": "DizzyHavoc", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "DizzyHavoc", "decimals": 18}], "OATH": [{"network": "arbitrum", "address": "******************************************", "name": "Oath <PERSON>ken", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Oath <PERSON>ken", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Oath <PERSON>ken", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Oath <PERSON>ken", "decimals": 18}], "HAMBURGLAR": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "The Hamburglar", "decimals": 18}], "GNO": [{"network": "arbitrum", "address": "******************************************", "name": "Gnosis <PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Gnosis <PERSON>", "decimals": 18}], "SPEED": [{"network": "arbitrum", "address": "******************************************", "name": "LightSpeed", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LightSpeed", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SpeedThrone", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "LightSpeed", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "LightSpeed", "decimals": null}], "OP": [{"network": "arbitrum", "address": "******************************************", "name": "Optimism", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Optimism", "decimals": null}], "WM": [{"network": "arbitrum", "address": "******************************************", "name": "WrappedM by M^0", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "WrappedM by M^0", "decimals": 6}], "MATIC": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Polygon", "decimals": null}], "EUSD": [{"network": "arbitrum", "address": "******************************************", "name": "Electronic Dollar", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Electronic Dollar", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "eUSD", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ARYZE eUSD", "decimals": null}], "LOCUS": [{"network": "arbitrum", "address": "******************************************", "name": "Locus <PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Locus Chain", "decimals": 18}], "PARAM": [{"network": "arbitrum", "address": "******************************************", "name": "Param", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Param", "decimals": 18}], "ALCX": [{"network": "arbitrum", "address": "******************************************", "name": "Alchemix", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Alchemix", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Alchemix", "decimals": null}], "FUN": [{"network": "arbitrum", "address": "******************************************", "name": "Funicular", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "FunFair", "decimals": 8}], "GEMS": [{"network": "arbitrum", "address": "******************************************", "name": "GEMS", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GEMS", "decimals": 18}], "SRK": [{"network": "arbitrum", "address": "******************************************", "name": "SparkPoint", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SparkPoint", "decimals": 18}], "KING": [{"network": "arbitrum", "address": "******************************************", "name": "King <PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "KING", "decimals": 18}], "SX": [{"network": "arbitrum", "address": "******************************************", "name": "SX Network", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SX Network", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SecretX", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "SportX (PoS)", "decimals": null}], "ICE": [{"network": "arbitrum", "address": "******************************************", "name": "Ice", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Ice", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "IceToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Decentral Games ICE", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Iron Finance ICE Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "IceToken", "decimals": 18}], "AKITA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}], "XCHNG": [{"network": "arbitrum", "address": "******************************************", "name": "Chainge", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Chainge", "decimals": 18}], "YUSD": [{"network": "arbitrum", "address": "******************************************", "name": "YieldFi yToken", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "YUSD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "YieldFi yToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "YieldFi yUSD", "decimals": 18}], "SUSDE": [{"network": "arbitrum", "address": "******************************************", "name": "Staked USDe", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Staked USDe", "decimals": 18}], "AGETH": [{"network": "arbitrum", "address": "******************************************", "name": "agETHWrapper", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "EDU": [{"network": "arbitrum", "address": "******************************************", "name": "EDU Coin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "EDU Coin", "decimals": 18}], "XETH": [{"network": "arbitrum", "address": "******************************************", "name": "Leveraged ETH", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Leveraged ETH", "decimals": 18}], "AURABAL": [{"network": "arbitrum", "address": "******************************************", "name": "Aura BAL", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Aura BAL", "decimals": 18}], "FTM": [{"network": "arbitrum", "address": "******************************************", "name": "Fantom <PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Fantom <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Fantom <PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "LFG": [{"network": "arbitrum", "address": "******************************************", "name": "LfgSwap Finance Token", "decimals": null}, {"network": "arbitrum", "address": "******************************************", "name": "LFG Club", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "LFG Club", "decimals": 18}], "HEZ": [{"network": "arbitrum", "address": "******************************************", "name": "Henez", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Hermez Network Token", "decimals": 18}], "GPT": [{"network": "arbitrum", "address": "******************************************", "name": "GPT", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "CryptoGPT Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GPT Protocol", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ChatGPT", "decimals": 9}], "SNT": [{"network": "arbitrum", "address": "******************************************", "name": "Status Network Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Status Network Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Status Network Token", "decimals": null}], "PLX": [{"network": "arbitrum", "address": "******************************************", "name": "Plexus", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyDEX.Fi 2.0", "decimals": 18}], "SDT": [{"network": "arbitrum", "address": "******************************************", "name": "Stake DAO Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Stake DAO Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Stake DAO Token (PoS)", "decimals": 18}], "RAI": [{"network": "arbitrum", "address": "******************************************", "name": "Rai Reflex Index", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Reploy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Rai Reflex Index", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Rai Reflex Index", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Rai Reflex Index (PoS)", "decimals": 18}], "APEUSD": [{"network": "arbitrum", "address": "******************************************", "name": "Ape USD", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "ApeUSD", "decimals": 18}], "OCEAN": [{"network": "arbitrum", "address": "******************************************", "name": "Ocean Token", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Ocean Token", "decimals": 18}], "APU": [{"network": "arbitrum", "address": "******************************************", "name": "Apu Apus<PERSON>ja", "decimals": null}, {"network": "base", "address": "******************************************", "name": "BASED APU", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Apu Apus<PERSON>ja", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Apu Apus<PERSON>ja", "decimals": 8}], "CAH": [{"network": "arbitrum", "address": "******************************************", "name": "Moon Tropica", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Moon Tropica", "decimals": 18}], "SOTU": [{"network": "arbitrum", "address": "******************************************", "name": "Seed Of The Universe", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Seed Of The Universe", "decimals": 18}], "ENS": [{"network": "arbitrum", "address": "******************************************", "name": "Ethereum Name Service", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Ethereum Name Service", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Ethereum Name Service", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Ethereum Name Service (PoS)", "decimals": null}], "KITTY": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "CryptoKitties [Gen 0]", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Kitty Coin", "decimals": 18}], "SHEKEL": [{"network": "arbitrum", "address": "******************************************", "name": "SHEKEL", "decimals": null}, {"network": "base", "address": "******************************************", "name": "Kosher Capital", "decimals": 18}], "TOIL": [{"network": "arbitrum", "address": "******************************************", "name": "Toil", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Toil", "decimals": 18}], "VIDYA": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "BFF": [{"network": "arbitrum", "address": "******************************************", "name": "Bunny Fu FU", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "BossFrog<PERSON><PERSON><PERSON>", "decimals": 9}], "MK": [{"network": "arbitrum", "address": "******************************************", "name": "MK", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MemeLinked", "decimals": 18}], "SWISE": [{"network": "arbitrum", "address": "******************************************", "name": "StakeWise", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "StakeWise", "decimals": 18}], "CLPC": [{"network": "arbitrum", "address": "******************************************", "name": "CLP Coin", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "CLP Coin", "decimals": 0}, {"network": "optimism", "address": "******************************************", "name": "CLP Coin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "CLP Coin", "decimals": 0}], "NSA": [{"network": "arbitrum", "address": "******************************************", "name": "Network Spirituality Agency", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Network Spirituality Agency", "decimals": 6}], "BUCK": [{"network": "arbitrum", "address": "******************************************", "name": "<PERSON>", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "GME MASCOT", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": 18}], "MOODENG": [{"network": "arbitrum", "address": "******************************************", "name": "MOODENG", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "MOO DENG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "BELUGA": [{"network": "arbitrum", "address": "******************************************", "name": "Beluga", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "BELUGA", "decimals": 18}], "EIGEN": [{"network": "arbitrum", "address": "******************************************", "name": "Eigen", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "Eigen", "decimals": 18}], "SCAI": [{"network": "arbitrum", "address": "******************************************", "name": "SecureChain AI", "decimals": null}, {"network": "eth", "address": "******************************************", "name": "SecureChain AI", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "SecureChain AI", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "SecureChain AI", "decimals": null}], "SATO": [{"network": "base", "address": "******************************************", "name": "SATO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON> the Snake", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "SATO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SATO", "decimals": 9}], "VIRTUAL": [{"network": "base", "address": "******************************************", "name": "Virtual Protocol", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Virtual Protocol", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Virtuals Protocol", "decimals": 18}], "ZORA": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Zora AI", "decimals": 18}], "DRB": [{"network": "base", "address": "******************************************", "name": "DebtReliefBot", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DebtReliefBot", "decimals": 18}], "LUM": [{"network": "base", "address": "******************************************", "name": "luminous", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "luminous", "decimals": 18}], "TRUST": [{"network": "base", "address": "******************************************", "name": "$TRUST ME BROs", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "TRUST DAO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "In Vitalik We Trust", "decimals": 9}], "BDOGE": [{"network": "base", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BABY D.O.G.E", "decimals": 9}], "TOSHI": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "69420": [{"network": "base", "address": "******************************************", "name": "Coin 69420", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "69420", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "69420", "decimals": 9}], "TURBO": [{"network": "base", "address": "******************************************", "name": "Based Turbo", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Turbo", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "TURBO.WIN", "decimals": 18}], "PENGU": [{"network": "base", "address": "******************************************", "name": "Based Pengu", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Pudgy Penguins", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "GEKKO": [{"network": "base", "address": "******************************************", "name": "Gekko AI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "OMI": [{"network": "base", "address": "******************************************", "name": "OMI Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "OMI Token", "decimals": 18}], "MOG": [{"network": "base", "address": "******************************************", "name": "Mog Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Mog Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MOG CAT", "decimals": 18}], "RUSSELL": [{"network": "base", "address": "******************************************", "name": "RUSSELL", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "RUSSELL", "decimals": 18}], "PROMPT": [{"network": "base", "address": "******************************************", "name": "Prompt", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Prompt", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wayfinder", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "LayerPrompt AI", "decimals": 9}], "BRAIN": [{"network": "base", "address": "******************************************", "name": "Gigabrain", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON> Frog", "decimals": 18}], "MOCHI": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "POLY": [{"network": "base", "address": "******************************************", "name": "Polytrader", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "COIN": [{"network": "base", "address": "******************************************", "name": "Coin6900", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "COIN6900", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "CoinIt", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Community Optimized Investment Network", "decimals": 18}], "SEAM": [{"network": "base", "address": "******************************************", "name": "Seamless", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Seamless", "decimals": 18}], "MCADE": [{"network": "base", "address": "******************************************", "name": "Metacade", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Metacade", "decimals": 18}], "OKAYEG": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Okayeg On Ethirium", "decimals": 9}], "AERO": [{"network": "base", "address": "******************************************", "name": "Aerodrome", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Aerodrome Finance", "decimals": 18}], "SKYA": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "SPORT": [{"network": "base", "address": "******************************************", "name": "SPORT", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Sport", "decimals": 18}], "SKI": [{"network": "base", "address": "******************************************", "name": "SKI MASK DOG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "SKI MASK DOG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SKI MASK DOG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Ski Mask Dog", "decimals": 18}], "KAITO": [{"network": "base", "address": "******************************************", "name": "KAITO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "KAITO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "kaito", "decimals": 9}], "FWOG": [{"network": "base", "address": "******************************************", "name": "Based Fwog", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FWOG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "FWOG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FWOG", "decimals": 18}], "BASE": [{"network": "base", "address": "******************************************", "name": "BASE", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Base Protocol", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "STARBASE", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Base is for everyone", "decimals": 18}], "VADER": [{"network": "base", "address": "******************************************", "name": "VaderAI by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Vader", "decimals": 18}], "LUNA": [{"network": "base", "address": "******************************************", "name": "Luna by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wrapped LUNA Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "LUNA (Wormhole)", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "Luna by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "LUNA (Wormhole)", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped LUNA Token (PoS)", "decimals": null}], "SPECTRA": [{"network": "base", "address": "******************************************", "name": "Spectra Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Spectra Token", "decimals": 18}], "ANDY": [{"network": "base", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "BasedAndy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ANDY", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "aNDY", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON> By <PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "RWAI": [{"network": "base", "address": "******************************************", "name": "RWAI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "RealWorld AI", "decimals": 18}], "LESTER": [{"network": "base", "address": "******************************************", "name": "LESTER", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}], "KTA": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "KEYCAT": [{"network": "base", "address": "******************************************", "name": "Keyboard Cat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Keyboard Cat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "KEYCAT", "decimals": 18}], "BRIAN": [{"network": "base", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}], "AIXBT": [{"network": "base", "address": "******************************************", "name": "aixbt by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "aixbt by Virtuals", "decimals": 18}], "TIBBIR": [{"network": "base", "address": "******************************************", "name": "Ribbita", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Ribbita by Virtuals", "decimals": 18}], "NXPC": [{"network": "base", "address": "******************************************", "name": "NEXPACE", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "NXPC", "decimals": 18}], "BYTE": [{"network": "base", "address": "******************************************", "name": "BYTE", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "Byte", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Byte", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "ByteAI", "decimals": 18}], "STONKS": [{"network": "base", "address": "******************************************", "name": "Stonks Base", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Stonks", "decimals": 9}], "SHOGUN": [{"network": "base", "address": "******************************************", "name": "Shogun", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SHOGUN", "decimals": 18}], "BRETT": [{"network": "base", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "BRETT", "decimals": 9}], "OBOT": [{"network": "base", "address": "******************************************", "name": "OBORTECH", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "OBORTECH", "decimals": 18}], "BOE": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bullish On Ethereum", "decimals": 9}], "BEPE": [{"network": "base", "address": "******************************************", "name": "BEPE", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "VVV": [{"network": "base", "address": "******************************************", "name": "Venice Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Credits", "decimals": 18}], "DOGE": [{"network": "base", "address": "******************************************", "name": "DOGECOIN", "decimals": 9}, {"network": "base", "address": "******************************************", "name": "Department Of Government Efficiency", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Department Of Government Efficiency", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Department Of Government Efficiency", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "D.O.G.E. Official <PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Dogecast", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Department of Government Efficiency", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "The King of Memes", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Department Of Government Efficiency", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Do Only Good Everyday", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "First Currency on Mars", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Do Only Good Everyday", "decimals": 9}], "DEGEN": [{"network": "base", "address": "******************************************", "name": "Degen", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Degen", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Degen", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Degen", "decimals": 9}], "XRP": [{"network": "base", "address": "******************************************", "name": "Based XRP", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "HarryPotterObamaPacMan8Inu", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "WarioXrpDumbledoreYugioh69Inu", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "HarryPotterTrumpPacMan8inu", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "XRP on Ethereum", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "WAI": [{"network": "base", "address": "******************************************", "name": "WAI Combinator", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Wiener AI", "decimals": 18}], "FAI": [{"network": "base", "address": "******************************************", "name": "FAI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FORTIFY AI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FetchAI", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Fortune AI Agent", "decimals": 6}], "PEEZY": [{"network": "base", "address": "******************************************", "name": "Young Peezy AKA Pepe", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 8}], "BTRST": [{"network": "base", "address": "******************************************", "name": "BTRST", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BTRST", "decimals": 18}], "BENJI": [{"network": "base", "address": "******************************************", "name": "Basenji", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BENJI", "decimals": 18}], "RWA": [{"network": "base", "address": "******************************************", "name": "RWA Inc", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "RWA-AI", "decimals": 18}], "NORMIE": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}], "OUSDT": [{"network": "base", "address": "******************************************", "name": "OpenUSDT", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Orbit Bridge Polygon Tether USD", "decimals": null}], "GLONK": [{"network": "base", "address": "******************************************", "name": "Glonk", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Glonk", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Glonk", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Glonk", "decimals": 18}], "MIGGLES": [{"network": "base", "address": "******************************************", "name": "Mister Miggles", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Mister Miggles", "decimals": 9}], "PRIME": [{"network": "base", "address": "******************************************", "name": "Prime", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Prime", "decimals": 18}], "SIGNAL": [{"network": "base", "address": "******************************************", "name": "Believe Signal", "decimals": 12}, {"network": "eth", "address": "******************************************", "name": "3motionGPT", "decimals": 9}], "ISLAND": [{"network": "base", "address": "******************************************", "name": "ISLAND", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ISLAND", "decimals": 18}], "BNKR": [{"network": "base", "address": "******************************************", "name": "BankrCoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BankrCoin", "decimals": 18}], "BOBO": [{"network": "base", "address": "******************************************", "name": "BOBO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BOBO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}], "FIRE": [{"network": "base", "address": "******************************************", "name": "Fire", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Matr1x Fire Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "FireBall", "decimals": 9}], "GAME": [{"network": "base", "address": "******************************************", "name": "GAME by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GAME Credits", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GameBuild", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Game", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Game Coin", "decimals": 5}, {"network": "eth", "address": "******************************************", "name": "GAME by Virtuals", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GAME Credits", "decimals": 18}], "REI": [{"network": "base", "address": "******************************************", "name": "Unit 00 - Rei", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Unit 00 - Rei", "decimals": 18}], "EXTRA": [{"network": "base", "address": "******************************************", "name": "Extra Finance", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Extra Finance", "decimals": null}], "SPX": [{"network": "base", "address": "******************************************", "name": "SPX6900", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "SPX6900", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "S&amp;P 500", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "SPX69000", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "S&amp;P 500", "decimals": null}], "JAV": [{"network": "base", "address": "******************************************", "name": "Javsphere", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Javtoken", "decimals": 8}], "B3": [{"network": "base", "address": "******************************************", "name": "B3", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Ryoshi B3 Vision", "decimals": 18}], "EYE": [{"network": "base", "address": "******************************************", "name": "Eye Future", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Behodler.io", "decimals": 18}], "XCN": [{"network": "base", "address": "******************************************", "name": "Onyxcoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Onyxcoin", "decimals": 18}], "HELP": [{"network": "base", "address": "******************************************", "name": "Help", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}], "GROGGO": [{"network": "base", "address": "******************************************", "name": "GROGGO", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Groggo By <PERSON>", "decimals": 18}], "EDGE": [{"network": "base", "address": "******************************************", "name": "Definitive", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Edge", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Edge AI", "decimals": 18}], "TRUMP": [{"network": "base", "address": "******************************************", "name": "MAGA", "decimals": 18}, {"network": "base", "address": "******************************************", "name": "MAGA", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "MAGA", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "BOME TRUMP", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MAGA", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "trumpwifhat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FreeTrump", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Official <PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MAGA", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "TRUMP COINS", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "YUGE", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Official Trump", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Official Trump", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "TRUMP", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "OFFICIAL TRUMP", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BITCOIN PRESIDENT", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Official Trump", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "OFFICIAL TRUMP", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "VIRTU": [{"network": "base", "address": "******************************************", "name": "Virtu  by Virtuals", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Virtu Network", "decimals": 18}], "T": [{"network": "base", "address": "******************************************", "name": "Threshold Network Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Threshold Network Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Threshold Network Token", "decimals": null}], "SPEC": [{"network": "base", "address": "******************************************", "name": "Spectral Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Spectral Token", "decimals": 18}], "BDAG": [{"network": "base", "address": "******************************************", "name": "BlockDAG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BlockDAG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BlockDAG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BlockDAG Network", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BlockDAG", "decimals": 9}], "USA": [{"network": "base", "address": "******************************************", "name": "BASED USA", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "America", "decimals": 18}], "MAVIA": [{"network": "base", "address": "******************************************", "name": "Heroes of Mavia", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Heroes of Mavia", "decimals": 18}], "SYRUP": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "BAMBOO": [{"network": "base", "address": "******************************************", "name": "Bamboo on Base", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BambooDeFi", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BambooDeFi", "decimals": 18}], "DMND": [{"network": "base", "address": "******************************************", "name": "Diamond", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Dmnd Token", "decimals": 18}], "MODE": [{"network": "base", "address": "******************************************", "name": "MODE", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "MODE", "decimals": null}], "MORPHO": [{"network": "base", "address": "******************************************", "name": "Morpho Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Morpho Token", "decimals": 18}], "DREAM": [{"network": "base", "address": "******************************************", "name": "dreamcoins", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DREAM", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "The American Dream", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "DREAM", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Decentralized.", "decimals": 18}], "WLFI": [{"network": "base", "address": "******************************************", "name": "World Liberty Financial", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "World Liberty Financial", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "World Liberty Fi", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "World Liberty Financial", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "World Liberty Financial", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "World Liberty Financial", "decimals": 9}], "WELL": [{"network": "base", "address": "******************************************", "name": "WELL", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Well", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "WELL", "decimals": null}], "NPC": [{"network": "base", "address": "******************************************", "name": "Non-Playable Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Non-Playable Coin", "decimals": 18}], "AETHER": [{"network": "base", "address": "******************************************", "name": "aethernet", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AETHER", "decimals": 18}], "RATO": [{"network": "base", "address": "******************************************", "name": "RATO the Rat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}], "OM": [{"network": "base", "address": "******************************************", "name": "MANTRA", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MANTRA DAO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "OM Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MANTRA DAO (PoS)", "decimals": 18}], "SNX": [{"network": "base", "address": "******************************************", "name": "Synthetix Network Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Synthetix Network Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Synthetix Network Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Synthetix Network Token (PoS)", "decimals": 18}], "DYOR": [{"network": "base", "address": "******************************************", "name": "DYOR", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DYOR", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DYOR Agent", "decimals": 18}], "GLUTEU": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> by Virtuals", "decimals": 18}], "BID": [{"network": "base", "address": "******************************************", "name": "CreatorBid", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bidao", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "TopBidder", "decimals": 18}], "SOVRN": [{"network": "base", "address": "******************************************", "name": "SOVRUN", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SOVRUN", "decimals": 18}], "SOSO": [{"network": "base", "address": "******************************************", "name": "SoSoValue", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SoSoValue", "decimals": 18}], "HENLO": [{"network": "base", "address": "******************************************", "name": "henlo", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "henlo", "decimals": 9}], "SUMMER": [{"network": "base", "address": "******************************************", "name": "Onchain Summer", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Summer Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Summer Token (PoS)", "decimals": 18}], "HUNT": [{"network": "base", "address": "******************************************", "name": "HuntToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "HuntToken", "decimals": 18}], "ORA": [{"network": "base", "address": "******************************************", "name": "ORA Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ORA Coin", "decimals": 18}], "KLIMA": [{"network": "base", "address": "******************************************", "name": "Klima DAO", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Klima DAO", "decimals": 9}], "CHAMP": [{"network": "base", "address": "******************************************", "name": "Super Champs", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Super Champs", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "NFT Champions", "decimals": null}], "ALU": [{"network": "base", "address": "******************************************", "name": "Altura", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Altura", "decimals": 18}], "ROCKET": [{"network": "base", "address": "******************************************", "name": "AI ROCKET", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Project Rocket", "decimals": 9}], "TONY": [{"network": "base", "address": "******************************************", "name": "<PERSON> Tony", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}], "VISION": [{"network": "base", "address": "******************************************", "name": "VISION ai", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Vision Token (PoS)", "decimals": 18}], "DAG": [{"network": "base", "address": "******************************************", "name": "Constellation", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Constellation", "decimals": 8}], "CLX": [{"network": "base", "address": "******************************************", "name": "Clanker Index", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "CloneX AI", "decimals": 9}], "RAIN": [{"network": "base", "address": "******************************************", "name": "Rain", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Precipitate.AI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Rainmaker Games", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Rain Coin", "decimals": 18}], "BITCOIN": [{"network": "base", "address": "******************************************", "name": "HarryPotterObamaSonic10Inu", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "HarryPotterObamaSonic10Inu", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "HarryPotterObamaSonic10Inu 2.0", "decimals": 18}], "CTO": [{"network": "base", "address": "******************************************", "name": "BaseCTO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Chief Troll Officer", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Ethereum CTO", "decimals": 9}], "AVA": [{"network": "base", "address": "******************************************", "name": "AVA", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AVA", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Audiovisual Advance", "decimals": 18}], "CHAD": [{"network": "base", "address": "******************************************", "name": "Based Chad", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "CHAD FROG", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Chad Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Chad", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Chad", "decimals": 18}], "HANA": [{"network": "base", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}], "DINO": [{"network": "base", "address": "******************************************", "name": "DINO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "DinoLFG", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON> by <PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Dino coin", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "DinoS<PERSON>p (PoS)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "SXT": [{"network": "base", "address": "******************************************", "name": "Space and Time", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Space and Time", "decimals": 18}], "SHIB": [{"network": "eth", "address": "******************************************", "name": "SHIBA INU", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "NicCageWaluigiElmo42069Inu", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Strategic Hub for Innovation in Blockchain", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Strategic Hub for Improved Bureaucracy", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "SHIBA INU (PoS)", "decimals": 18}], "TOMI": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "TOMI", "decimals": null}], "ELON": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Elon", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "The GrokFather", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "RNDR": [{"network": "eth", "address": "******************************************", "name": "Render Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Render Token", "decimals": 18}], "WLD": [{"network": "eth", "address": "******************************************", "name": "Worldcoin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Worldcoin", "decimals": null}], "PIN": [{"network": "eth", "address": "******************************************", "name": "PinLink", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Pay it Now", "decimals": 18}], "SEI": [{"network": "eth", "address": "******************************************", "name": "SEI", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SEI", "decimals": 6}], "PAXG": [{"network": "eth", "address": "******************************************", "name": "Paxos Gold", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Paxos Gold (PoS)", "decimals": 18}], "FET": [{"network": "eth", "address": "******************************************", "name": "<PERSON>tch", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>tch (PoS)", "decimals": 18}], "DINERO": [{"network": "eth", "address": "******************************************", "name": "Dinero Governance Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "DINERO", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Dinero OFT", "decimals": null}], "UMA": [{"network": "eth", "address": "******************************************", "name": "UMA Voting Token v1", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "UMA Voting Token v1 (PoS)", "decimals": 18}], "MKR": [{"network": "eth", "address": "******************************************", "name": "Maker", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MAKER (PoS)", "decimals": 18}], "JASMY": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "XYO": [{"network": "eth", "address": "******************************************", "name": "XY Oracle", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "XY Oracle", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "XY Oracle (PoS)", "decimals": 18}], "SEN": [{"network": "eth", "address": "******************************************", "name": "Sentio Protocol", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Senspark", "decimals": 18}], "HEX": [{"network": "eth", "address": "******************************************", "name": "HEX", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "HEX from PulseChain", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "HEX (PoS)", "decimals": 8}], "ALT": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Alteco", "decimals": 18}], "BONE": [{"network": "eth", "address": "******************************************", "name": "BONE SHIBASWAP", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FLEABONE", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Bone", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Dog by <PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Bone by 𝓜𝓪𝓽𝓽 𝓕𝓾𝓻𝓲𝓮", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Book Of Neiro", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Bones", "decimals": 18}], "CRO": [{"network": "eth", "address": "******************************************", "name": "CRO", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "CRO (PoS)", "decimals": 8}], "TEL": [{"network": "eth", "address": "******************************************", "name": "Telcoin", "decimals": 2}, {"network": "polygon_pos", "address": "******************************************", "name": "Telcoin (PoS)", "decimals": 2}, {"network": "polygon_pos", "address": "******************************************", "name": "TEL Company №7", "decimals": 18}], "BANANA": [{"network": "eth", "address": "******************************************", "name": "Banana", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Banana", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Banana", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SpaceX Banana", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "ApeSwapFinance Banana", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Banana", "decimals": 18}], "ZIG": [{"network": "eth", "address": "******************************************", "name": "<PERSON>ig<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>ig<PERSON><PERSON><PERSON>", "decimals": 18}], "BNB": [{"network": "eth", "address": "******************************************", "name": "BNB", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Binance", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Binance Token", "decimals": 18}], "ACX": [{"network": "eth", "address": "******************************************", "name": "Across Protocol Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Across Protocol Token", "decimals": null}], "SUPER": [{"network": "eth", "address": "******************************************", "name": "SuperFarm", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SuperFarm (PoS)", "decimals": 18}], "NEXO": [{"network": "eth", "address": "******************************************", "name": "Nexo", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Nexo (PoS)", "decimals": 18}], "BEAST": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Beast", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Beast", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "BEAST", "decimals": null}], "BOB": [{"network": "eth", "address": "******************************************", "name": "BOB", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "BOB", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "BOB", "decimals": 18}], "SAND": [{"network": "eth", "address": "******************************************", "name": "SAND", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SAND", "decimals": 18}], "VANRY": [{"network": "eth", "address": "******************************************", "name": "VANRY", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "VANRY", "decimals": 18}], "NITRO": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "TOWER": [{"network": "eth", "address": "******************************************", "name": "TOWER", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "TOWER", "decimals": 18}], "MOCA": [{"network": "eth", "address": "******************************************", "name": "Moca", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Museum of Crypto Art", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Museum of Crypto Art", "decimals": null}], "GFI": [{"network": "eth", "address": "******************************************", "name": "Goldfinch", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Gravity Finance", "decimals": 18}], "TREAT": [{"network": "eth", "address": "******************************************", "name": "Shiba Inu Treat", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "VERSE": [{"network": "eth", "address": "******************************************", "name": "Verse", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Shibaverse", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Shibaverse", "decimals": null}], "MANA": [{"network": "eth", "address": "******************************************", "name": "Decentraland MANA", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Decentraland MANA", "decimals": 18}], "MASK": [{"network": "eth", "address": "******************************************", "name": "Mask Network", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "MaskDAO", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Mask Network (PoS)", "decimals": 18}], "SGT": [{"network": "eth", "address": "******************************************", "name": "SGT", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Sharedstake.finance", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Shill Guard Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "snglsDAO Governance Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SGT (PoS)", "decimals": 8}], "CYBER": [{"network": "eth", "address": "******************************************", "name": "CyberConnect", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Cybertruck", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "CyberConnect", "decimals": null}], "BAT": [{"network": "eth", "address": "******************************************", "name": "Basic Attention Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BATO THE BAT", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "BATIC", "decimals": 18}], "KEK": [{"network": "eth", "address": "******************************************", "name": "Kekistan", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "EI Risitas", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Origin Of Pepe", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Kekcoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Pepe Prophecy", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "KEK", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Kek", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Cult of KEK", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Aavegotchi KEK", "decimals": 18}], "ALI": [{"network": "eth", "address": "******************************************", "name": "Artificial Liquid Intelligence Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Artificial Liquid Intelligence Token", "decimals": 18}], "AGIX": [{"network": "eth", "address": "******************************************", "name": "SingularityNET Token", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "SingularityNET Token (PoS)", "decimals": 8}], "CSWAP": [{"network": "eth", "address": "******************************************", "name": "ChainSwap", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CSWAP", "decimals": 18}], "SOPH": [{"network": "eth", "address": "******************************************", "name": "SOPH AI Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "BCUT": [{"network": "eth", "address": "******************************************", "name": "bitsCrunch Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "bitsCrunch Token(PoS)", "decimals": 18}], "GMT": [{"network": "eth", "address": "******************************************", "name": "GoMining Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GreenMetaverseToken", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "GreenMetaverseToken", "decimals": 8}], "SD": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "DOGEVERSE": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}], "PSP": [{"network": "eth", "address": "******************************************", "name": "ParaSwap", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "ParaSwap", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "ParaSwap (PoS)", "decimals": 18}], "PERP": [{"network": "eth", "address": "******************************************", "name": "Perpetual", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Perpetual", "decimals": null}], "UST": [{"network": "eth", "address": "******************************************", "name": "Wrapped UST Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "UST (Wormhole)", "decimals": 6}, {"network": "optimism", "address": "******************************************", "name": "TerraUSD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "UST (Wormhole)", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped UST Token (PoS)", "decimals": 18}], "VOLT": [{"network": "eth", "address": "******************************************", "name": "Volt Inu", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "VOLT.WIN", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Volt Inu", "decimals": 9}], "SAGE": [{"network": "eth", "address": "******************************************", "name": "Sage Market", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolySage", "decimals": 18}], "UBT": [{"network": "eth", "address": "******************************************", "name": "UniBright", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": null}], "1INCH": [{"network": "eth", "address": "******************************************", "name": "1INCH Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "1Inch (PoS)", "decimals": 18}], "YGG": [{"network": "eth", "address": "******************************************", "name": "Yield Guild Games Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Yield Guild Games Token (PoS)", "decimals": null}], "COR": [{"network": "eth", "address": "******************************************", "name": "<PERSON>rten<PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "COR Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Coreto (PoS)", "decimals": 18}], "REQ": [{"network": "eth", "address": "******************************************", "name": "Request Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Request", "decimals": 18}], "GLQ": [{"network": "eth", "address": "******************************************", "name": "GraphLinq", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GraphLinq (PoS)", "decimals": 18}], "ALPHA": [{"network": "eth", "address": "******************************************", "name": "AlphaToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Alpha-Spotter", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Alpha", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Alpha", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Aavegotchi ALPHA", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyAlpha Finance", "decimals": null}], "PXETH": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "f(x) High Volatility ETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Pirex Ether OFT", "decimals": null}], "TRADE": [{"network": "eth", "address": "******************************************", "name": "Polytrade", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "UniTrade", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Polytrade (PoS)", "decimals": 18}], "MCO2": [{"network": "eth", "address": "******************************************", "name": "Moss Carbon Credit", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Moss Carbon Credit (PoS)", "decimals": 18}], "AXS": [{"network": "eth", "address": "******************************************", "name": "Axie Infinity Shard", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Axie <PERSON> Shard (PoS)", "decimals": 18}], "GLM": [{"network": "eth", "address": "******************************************", "name": "Golem Network Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Golem Network Token (PoS)", "decimals": 18}], "IQ": [{"network": "eth", "address": "******************************************", "name": "Everipedia IQ", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Everipedia IQ (PoS)", "decimals": null}], "0XBTC": [{"network": "eth", "address": "******************************************", "name": "0xBitcoin Token", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "0xBitcoin Token", "decimals": 8}], "SUSD": [{"network": "eth", "address": "******************************************", "name": "Synth sUSD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Synth sUSD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "SugarDollar", "decimals": 18}], "KAI": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "kai", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Komputai", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Kinetix Finance", "decimals": 18}], "REVV": [{"network": "eth", "address": "******************************************", "name": "REVV", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "REVV", "decimals": 18}], "XUSD": [{"network": "eth", "address": "******************************************", "name": "XUSD", "decimals": 6}, {"network": "eth", "address": "******************************************", "name": "XUSD Stablecoin", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "xDollar Stablecoin", "decimals": 18}], "USDM": [{"network": "eth", "address": "******************************************", "name": "Mountain Protocol USD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Mountain Protocol USD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Mountain Protocol USD", "decimals": 18}], "NEAR": [{"network": "eth", "address": "******************************************", "name": "NEAR", "decimals": 24}, {"network": "polygon_pos", "address": "******************************************", "name": "NEAR", "decimals": 18}], "DYDX": [{"network": "eth", "address": "******************************************", "name": "dYdX", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "dYdX (PoS)", "decimals": null}], "SWAP": [{"network": "eth", "address": "******************************************", "name": "TrustSwap Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "TrustSwap Token (PoS)", "decimals": 18}], "WISE": [{"network": "eth", "address": "******************************************", "name": "Wise Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wise <PERSON>ken (PoS)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyWise", "decimals": 18}], "00": [{"network": "eth", "address": "******************************************", "name": "00 Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "00 Token", "decimals": 18}], "MOLLY": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "ANKR": [{"network": "eth", "address": "******************************************", "name": "Ankr Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Ankr (PoS)", "decimals": 18}], "THREE": [{"network": "eth", "address": "******************************************", "name": "Three Protocol Token", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "ThreeCoin", "decimals": null}], "AURORA": [{"network": "eth", "address": "******************************************", "name": "Aurora", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "New Grok Model", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "AuroraToken", "decimals": 18}], "FRM": [{"network": "eth", "address": "******************************************", "name": "Ferrum Network Token", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Ferrum Network Token", "decimals": 18}], "VAI": [{"network": "eth", "address": "******************************************", "name": "VAIOT Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "VAIOT Token", "decimals": 18}], "VUSD": [{"network": "eth", "address": "******************************************", "name": "vUSD", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "VUSD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "vUSD", "decimals": null}], "3CRV": [{"network": "eth", "address": "******************************************", "name": "Curve.fi DAI/USDC/USDT", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Curve.fi DAI/USDC/USDT", "decimals": null}], "XSGD": [{"network": "eth", "address": "******************************************", "name": "XSGD", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "XSGD", "decimals": 6}], "WCT": [{"network": "eth", "address": "******************************************", "name": "WalletConnect", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "WalletConnect", "decimals": null}], "GAMMA": [{"network": "eth", "address": "******************************************", "name": "Gamma", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyGamma Finance", "decimals": null}], "GX": [{"network": "eth", "address": "******************************************", "name": "Grindery X", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Grindery X", "decimals": null}], "KAS": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 8}], "GMEE": [{"network": "eth", "address": "******************************************", "name": "GAMEE", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GAMEE", "decimals": null}], "WCHI": [{"network": "eth", "address": "******************************************", "name": "Wrapped CHI", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped CHI (PoS)", "decimals": null}], "GNUS": [{"network": "eth", "address": "******************************************", "name": "Genius Token &amp; NFT Collections", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Genius Token &amp; NFT Collections", "decimals": 18}], "OVR": [{"network": "eth", "address": "******************************************", "name": "OVR", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "OVR (PoS)", "decimals": 18}], "BOLD": [{"network": "eth", "address": "******************************************", "name": "Bold Stablecoin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Bold Stablecoin", "decimals": null}], "RAMP": [{"network": "eth", "address": "******************************************", "name": "RAMP DEFI", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "RAMP (PoS)", "decimals": 18}], "CIOTX": [{"network": "eth", "address": "******************************************", "name": "Crosschain IOTX", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Crosschain IOTX", "decimals": 18}], "EURS": [{"network": "eth", "address": "******************************************", "name": "STASIS EURS Token", "decimals": 2}, {"network": "polygon_pos", "address": "******************************************", "name": "STASIS EURS Token (PoS)", "decimals": null}], "SLP": [{"network": "eth", "address": "******************************************", "name": "Smooth Love Potion", "decimals": 0}, {"network": "eth", "address": "******************************************", "name": "SakeSwap LP Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SushiSwap LP Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SushiSwap LP Token", "decimals": null}], "DPI": [{"network": "eth", "address": "******************************************", "name": "DefiPulse Index", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DefiPulse Index (PoS)", "decimals": 18}], "ORBS": [{"network": "eth", "address": "******************************************", "name": "Or<PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Orbs (PoS)", "decimals": 18}], "ZIK": [{"network": "eth", "address": "******************************************", "name": "Ziktalk", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ZIK coin", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Ziktalk (PoS)", "decimals": 18}], "BAG": [{"network": "eth", "address": "******************************************", "name": "Bag", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Blockchain Adventurers Guild", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "tehBag", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bag", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Bag(PoS)", "decimals": 18}], "EURT": [{"network": "eth", "address": "******************************************", "name": "Euro Tether", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Euro Tether (PoS)", "decimals": 6}], "BOSON": [{"network": "eth", "address": "******************************************", "name": "<PERSON>son <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "SFI": [{"network": "eth", "address": "******************************************", "name": "Singularity Finance", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Spice", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Spice (PoS)", "decimals": 18}], "GEL": [{"network": "eth", "address": "******************************************", "name": "Gelato Network Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Gelato Network Token", "decimals": null}], "SG": [{"network": "eth", "address": "******************************************", "name": "SocialGood", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SocialGood (PoS)", "decimals": 18}], "CXO": [{"network": "eth", "address": "******************************************", "name": "CargoX Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CargoX Token (PoS)", "decimals": 18}], "ZERC": [{"network": "eth", "address": "******************************************", "name": "zkRace", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "zkRace", "decimals": 18}], "POLS": [{"network": "eth", "address": "******************************************", "name": "PolkastarterToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Bridged prc-20 pols", "decimals": 18}], "FORT": [{"network": "eth", "address": "******************************************", "name": "Forta", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "FortToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Forta", "decimals": 18}], "FARM": [{"network": "eth", "address": "******************************************", "name": "FARM Reward <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CryptoFarmers", "decimals": null}], "MNW": [{"network": "eth", "address": "******************************************", "name": "Morpheus.Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Morpheus.Network (PoS)", "decimals": 18}], "DATA": [{"network": "eth", "address": "******************************************", "name": "Streamr", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Data bot", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Streamr", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DATA Economy Index (PoS)", "decimals": null}], "SURE": [{"network": "eth", "address": "******************************************", "name": "inSure", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "inSure (PoS)", "decimals": 18}], "ERN": [{"network": "eth", "address": "******************************************", "name": "@EthernityChain $ERN Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Ethos Reserve Note", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Ethernity Chain (PoS)", "decimals": 18}], "DEURO": [{"network": "eth", "address": "******************************************", "name": "DecentralizedEURO", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DecentralizedEURO(PoS)", "decimals": 18}], "ZARP": [{"network": "eth", "address": "******************************************", "name": "ZARP Stablecoin", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ZARP Stablecoin", "decimals": 18}], "RADAR": [{"network": "eth", "address": "******************************************", "name": "DappRadar", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Radar AI", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "DappRadar", "decimals": 18}], "OPN": [{"network": "eth", "address": "******************************************", "name": "Open Ecosystem Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Open Ecosystem Token(PoS)", "decimals": 18}], "BTC": [{"network": "eth", "address": "******************************************", "name": "BlackrockTradingCurrency", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "bitcoin", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "Boost <PERSON> Campaign", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Bullish Trump Coin", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "HarryPotterTrumpSonic100Inu", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "NETVR": [{"network": "eth", "address": "******************************************", "name": "NetVRk", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "NetVRk", "decimals": null}], "CEL": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 4}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": null}], "RENBTC": [{"network": "eth", "address": "******************************************", "name": "renBTC", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "renBTC", "decimals": null}], "APP": [{"network": "eth", "address": "******************************************", "name": "Moon App", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "APP High-yield and risk company №9", "decimals": 18}], "NCT": [{"network": "eth", "address": "******************************************", "name": "Nectar", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Toucan Protocol: Nature Carbon Tonne", "decimals": 18}], "ORC": [{"network": "eth", "address": "******************************************", "name": "Orbit Chain", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Orbit Chain (PoS)", "decimals": 18}], "CHAIN": [{"network": "eth", "address": "******************************************", "name": "Chain Games", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Chain Games", "decimals": 18}], "PUSH": [{"network": "eth", "address": "******************************************", "name": "Ethereum Push Notification Service", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Ethereum Push Notification Service (PoS)", "decimals": null}], "BUSD": [{"network": "eth", "address": "******************************************", "name": "Binance USD", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BUSD Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "(PoS) Binance USD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "BUSD Token (Wormhole)", "decimals": null}], "BUILD": [{"network": "eth", "address": "******************************************", "name": "BuildAI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Build Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BuildAI", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Build Token", "decimals": 18}], "CAL": [{"network": "eth", "address": "******************************************", "name": "Calcium", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CALVEN J Token", "decimals": 18}], "PNT": [{"network": "eth", "address": "******************************************", "name": "pNetwork Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Ponos ant", "decimals": 18}], "ELAND": [{"network": "eth", "address": "******************************************", "name": "Etherland", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Etherland (PoS)", "decimals": null}], "DG": [{"network": "eth", "address": "******************************************", "name": "DeGate <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Decentral Games (PoS)", "decimals": null}], "ADS": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 11}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": 11}], "BANK": [{"network": "eth", "address": "******************************************", "name": "Float Bank", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bank Ai", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bankless Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Bankless Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Bankless Token (PoS)", "decimals": 18}], "WAVAX": [{"network": "eth", "address": "******************************************", "name": "Wrapped AVAX (Wormhole)", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped AVAX (Wormhole)", "decimals": null}], "BONDLY": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "SUKU": [{"network": "eth", "address": "******************************************", "name": "SUKU", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SUKU (PoS)", "decimals": 18}], "MSETH": [{"network": "eth", "address": "******************************************", "name": "Metronome Synth ETH", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Metronome Synth ETH", "decimals": null}], "COLLAR": [{"network": "eth", "address": "******************************************", "name": "DOG COLLAR", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyPup Collar <PERSON>", "decimals": 18}], "MYST": [{"network": "eth", "address": "******************************************", "name": "Mysterium", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MyStandard", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Mysterium (PoS)", "decimals": 18}], "PROS": [{"network": "eth", "address": "******************************************", "name": "Prosper", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Prospective", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Prospective", "decimals": 18}], "SFRAX": [{"network": "eth", "address": "******************************************", "name": "Staked FRAX", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Staked FRAX", "decimals": null}], "JEFF": [{"network": "eth", "address": "******************************************", "name": "JEFF", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Polygon Mascot", "decimals": 18}], "MILK": [{"network": "eth", "address": "******************************************", "name": "MILK", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MilkToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MILK", "decimals": 18}], "GEN": [{"network": "eth", "address": "******************************************", "name": "Generational Wealth", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>-<PERSON>an", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON> - <PERSON><PERSON>'s brother", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "WRLD": [{"network": "eth", "address": "******************************************", "name": "NFT Worlds", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "NFT Worlds", "decimals": 18}], "GREEN": [{"network": "eth", "address": "******************************************", "name": "Green", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "Greenhouse", "decimals": 18}], "RISE": [{"network": "eth", "address": "******************************************", "name": "EverRise", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "AdRise", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "EverRise", "decimals": null}], "BULL": [{"network": "eth", "address": "******************************************", "name": "Bull Market", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "ETH BULL", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Ethereum is good", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Ethereum is Good", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Bullionaire", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Bullieverse", "decimals": 18}], "FRONT": [{"network": "eth", "address": "******************************************", "name": "Frontier Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Frontier Token (PoS)", "decimals": 18}], "PLR": [{"network": "eth", "address": "******************************************", "name": "PILLAR", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PILLAR (PoS)", "decimals": 18}], "IDEX": [{"network": "eth", "address": "******************************************", "name": "IDEX Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "IDEX Token (PoS)", "decimals": 18}], "TUSD": [{"network": "eth", "address": "******************************************", "name": "TrueUSD", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "TrueUSD", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "TrueUSD (PoS)", "decimals": 18}], "PBTC": [{"network": "eth", "address": "******************************************", "name": "pTokens BTC", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "pTokens BTC", "decimals": null}], "FUSE": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": null}], "UFT": [{"network": "eth", "address": "******************************************", "name": "UniLend Finance Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "UniLend Finance Token (PoS)", "decimals": 18}], "BLOOD": [{"network": "eth", "address": "******************************************", "name": "Blood", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Blood Token", "decimals": 18}], "WOMBAT": [{"network": "eth", "address": "******************************************", "name": "Wombat", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Wombat", "decimals": 18}], "DERC": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}], "GENE": [{"network": "eth", "address": "******************************************", "name": "GenomesDAO", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "GeneToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GenomesDAO (PoS)", "decimals": 18}], "GET": [{"network": "eth", "address": "******************************************", "name": "GET", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GET Protocol (PoS)", "decimals": null}], "BORING": [{"network": "eth", "address": "******************************************", "name": "BoringDAO", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BoringDAO (PoS)", "decimals": 18}], "FEAR": [{"network": "eth", "address": "******************************************", "name": "Fear NFTs", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Fear NFTs (PoS)", "decimals": 18}], "$DG": [{"network": "eth", "address": "******************************************", "name": "decentral.games", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "decentral.games (PoS)", "decimals": 18}], "DEFI": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Defiway To<PERSON>", "decimals": 18}], "NEX": [{"network": "eth", "address": "******************************************", "name": "NEXUS", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Nash Exchange Token (PoS)", "decimals": 8}], "BAO": [{"network": "eth", "address": "******************************************", "name": "BaoToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Bao Token V2", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "BetTensor", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BaoToken (PoS)", "decimals": 18}], "SOIL": [{"network": "eth", "address": "******************************************", "name": "Soil", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Soil", "decimals": 18}], "PAR": [{"network": "eth", "address": "******************************************", "name": "PAR Stablecoin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Parachute", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PAR Stablecoin", "decimals": 18}], "SNOW": [{"network": "eth", "address": "******************************************", "name": "Snowman", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SnowSwap", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SnowSwap (PoS)", "decimals": 18}], "IDRT": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 2}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 6}], "CTF": [{"network": "eth", "address": "******************************************", "name": "Crypto Task Force", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Crypto Trading Fund", "decimals": 18}], "DFYN": [{"network": "eth", "address": "******************************************", "name": "DFYN Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DFYN Token (PoS)", "decimals": 18}], "BETA": [{"network": "eth", "address": "******************************************", "name": "Beta Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Beta Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyBeta Finance", "decimals": 18}], "CNTR": [{"network": "eth", "address": "******************************************", "name": "Centaur Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Centaur Token (PoS)", "decimals": null}], "COSMIC": [{"network": "eth", "address": "******************************************", "name": "Cosmic Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CosmicSwap", "decimals": null}], "SAIL": [{"network": "eth", "address": "******************************************", "name": "SAIL Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "SAIL Token", "decimals": null}], "EQUAD": [{"network": "eth", "address": "******************************************", "name": "QuadrantProtocol", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Quadrant Protocol", "decimals": 18}], "HANEP": [{"network": "eth", "address": "******************************************", "name": "HANePlatform", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "HANePlatform", "decimals": null}], "BITT": [{"network": "eth", "address": "******************************************", "name": "BITTOKEN", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BITTOKEN (PoS)", "decimals": 18}], "INSUR": [{"network": "eth", "address": "******************************************", "name": "InsurAce", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "InsurAce (PoS)", "decimals": 18}], "FOLO": [{"network": "eth", "address": "******************************************", "name": "Follow", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Follow (PoS)", "decimals": null}], "PAW": [{"network": "eth", "address": "******************************************", "name": "PAW", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "PAWZONE", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "PAWSWAP", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Paw V2", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>w", "decimals": 18}], "ITP": [{"network": "eth", "address": "******************************************", "name": "Interport Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Infinite Trading Protocol", "decimals": null}], "ABOND": [{"network": "eth", "address": "******************************************", "name": "ApeBond", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ApeBond", "decimals": null}], "DNOW": [{"network": "eth", "address": "******************************************", "name": "DuelNow", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DuelNow", "decimals": 8}], "QUICK": [{"network": "eth", "address": "******************************************", "name": "Quickswap", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "QuickSwap", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "QuickSwap", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Quickswap", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Quick Drop", "decimals": null}], "SNL": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "ALTS": [{"network": "eth", "address": "******************************************", "name": "Alts", "decimals": 12}, {"network": "polygon_pos", "address": "******************************************", "name": "ALTS-chain", "decimals": 18}], "RENDOGE": [{"network": "eth", "address": "******************************************", "name": "renDOGE", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "renDOGE", "decimals": 8}], "HAN": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": null}], "KROM": [{"network": "eth", "address": "******************************************", "name": "Kromatika", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Kromatika", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Kromatika (PoS)", "decimals": 18}], "MM": [{"network": "eth", "address": "******************************************", "name": "Million", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "MMToken", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Million (PoS)", "decimals": null}], "MC": [{"network": "eth", "address": "******************************************", "name": "Merit Circle", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MeldinCapital", "decimals": 18}], "MVI": [{"network": "eth", "address": "******************************************", "name": "Metaverse Index", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Metaverse Index (PoS)", "decimals": null}], "BALL": [{"network": "eth", "address": "******************************************", "name": "Game 5 BALL", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Dragon Ball Coin se7en", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Ball Token", "decimals": 18}], "BLANK": [{"network": "eth", "address": "******************************************", "name": "GoBlank Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Blank Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GoBlank <PERSON> (PoS)", "decimals": 18}], "KIT": [{"network": "eth", "address": "******************************************", "name": "DexKit", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "DexKit (PoS)", "decimals": 18}], "MPH": [{"network": "eth", "address": "******************************************", "name": "88mph.app", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "PLOT": [{"network": "eth", "address": "******************************************", "name": "PLOT", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PlotX", "decimals": 18}], "SOCKS": [{"network": "eth", "address": "******************************************", "name": "Unisocks Edition 0", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "socks", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "Unisocks Edition 0", "decimals": null}], "PUNK": [{"network": "eth", "address": "******************************************", "name": "CryptoPunks", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "PUNK", "decimals": null}], "HAIR": [{"network": "eth", "address": "******************************************", "name": "HairDAO Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "HairDAO Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Hair Token", "decimals": 18}], "MTA": [{"network": "eth", "address": "******************************************", "name": "Meta", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Meta", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Meta (PoS)", "decimals": 18}], "EURE": [{"network": "eth", "address": "******************************************", "name": "Monerium EUR emoney", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Monerium EUR emoney", "decimals": 18}], "PICKLE": [{"network": "eth", "address": "******************************************", "name": "PickleToken", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Pick<PERSON> The Pickle", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Pickle", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "The Magic Pickle", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PickleToken (PoS)", "decimals": 18}], "SLN": [{"network": "eth", "address": "******************************************", "name": "Smart Layer Network Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Smart Layer Network Token (PoS)", "decimals": 18}], "SHIBAKEN": [{"network": "eth", "address": "******************************************", "name": "ShibaKen.Finance", "decimals": 0}, {"network": "polygon_pos", "address": "******************************************", "name": "ShibaKen.Finance", "decimals": 0}], "PYR": [{"network": "eth", "address": "******************************************", "name": "PYR Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PYR Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PYR Token", "decimals": 18}], "MET": [{"network": "eth", "address": "******************************************", "name": "Metronome2", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Metronome2", "decimals": null}], "CTSI": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "FACTR": [{"network": "eth", "address": "******************************************", "name": "Defactor", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Defactor (PoS)", "decimals": 18}], "YIELD": [{"network": "eth", "address": "******************************************", "name": "Yield Protocol", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyYield Token", "decimals": 18}], "MONA": [{"network": "eth", "address": "******************************************", "name": "Monavale", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Monavale", "decimals": 18}], "GHST": [{"network": "eth", "address": "******************************************", "name": "Aavegotchi GHST Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Aavegotchi GHST Token (PoS)", "decimals": 18}], "KIRO": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "HANU": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 12}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 12}], "SBTC": [{"network": "eth", "address": "******************************************", "name": "Synth sBTC", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "StakeStone Bitcoin", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Synth sBTC", "decimals": null}], "BTBS": [{"network": "eth", "address": "******************************************", "name": "BitBase", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BTBSaga", "decimals": 18}], "HDAO": [{"network": "eth", "address": "******************************************", "name": "humanDAO", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "humanDAO (PoS)", "decimals": 18}], "SKRT": [{"network": "eth", "address": "******************************************", "name": "Sekuritance", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Sekuritance (PoS)", "decimals": 18}], "TCP": [{"network": "eth", "address": "******************************************", "name": "The Crypto Prophecies", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "The Crypto Prophecies (PoS)", "decimals": 18}], "TREND": [{"network": "eth", "address": "******************************************", "name": "TrendApp", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "TREND", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "TRENDY DEFI", "decimals": 18}], "NIOX": [{"network": "eth", "address": "******************************************", "name": "Autonio", "decimals": 4}, {"network": "polygon_pos", "address": "******************************************", "name": "Autonio (PoS)", "decimals": 4}], "MCHC": [{"network": "eth", "address": "******************************************", "name": "MCHCoin", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "LUCHOW": [{"network": "eth", "address": "******************************************", "name": "LunaChow", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "LunaChow", "decimals": null}], "CBY": [{"network": "eth", "address": "******************************************", "name": "Carbify", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Carbify", "decimals": 18}], "ORION": [{"network": "eth", "address": "******************************************", "name": "Orion Money Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Orion Money Token (Wormhole)", "decimals": null}], "BTU": [{"network": "eth", "address": "******************************************", "name": "BTU Protocol", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BTU Protocol (PoS)", "decimals": 18}], "THEOS": [{"network": "eth", "address": "******************************************", "name": "THEOS", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "THEOS (PoS)", "decimals": 18}], "PBR": [{"network": "eth", "address": "******************************************", "name": "PolkaBridge", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolkaBridge", "decimals": 18}], "AXI": [{"network": "eth", "address": "******************************************", "name": "Axioms", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Axioms (PoS)", "decimals": null}], "MEEB": [{"network": "eth", "address": "******************************************", "name": "Meebits", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MeebMaster.com Token", "decimals": 18}], "VBTC": [{"network": "eth", "address": "******************************************", "name": "Strudel BTC", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "vBTC", "decimals": null}], "GENI": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 9}], "HAI": [{"network": "eth", "address": "******************************************", "name": "Hacken Token", "decimals": 8}, {"network": "optimism", "address": "******************************************", "name": "HAI Index Token", "decimals": null}], "TXL": [{"network": "eth", "address": "******************************************", "name": "Tixl Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Tixl Token", "decimals": 18}], "GRAIN": [{"network": "eth", "address": "******************************************", "name": "GRAIN Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Granary <PERSON>", "decimals": null}], "WINTER": [{"network": "eth", "address": "******************************************", "name": "Winter Token", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Trump Family Dog", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Winter Token (PoS)", "decimals": 18}], "DHT": [{"network": "eth", "address": "******************************************", "name": "dHedge DAO Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "dHEDGE DAO Token", "decimals": null}], "CGG": [{"network": "eth", "address": "******************************************", "name": "ChainGuardians Governance Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ChainGuardians Governance Token (PoS)", "decimals": 18}], "AUTUMN": [{"network": "eth", "address": "******************************************", "name": "Autumn Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Autumn Token (PoS)", "decimals": 18}], "MUST": [{"network": "eth", "address": "******************************************", "name": "Must", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Must", "decimals": 18}], "DCI": [{"network": "eth", "address": "******************************************", "name": "Decentralized Cloud Infrastructure", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Dynamic Crypto Index", "decimals": 18}], "BOMB": [{"network": "eth", "address": "******************************************", "name": "BOMB", "decimals": 0}, {"network": "eth", "address": "******************************************", "name": "Adam Bomb Squad", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Bombcrypto Coin", "decimals": null}], "MIMO": [{"network": "eth", "address": "******************************************", "name": "MIMO Parallel Governance Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MIMO Parallel Governance Token (PoS)", "decimals": null}], "GALAXIS": [{"network": "eth", "address": "******************************************", "name": "GALAXIS Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "GALAXIS Token(PoS)", "decimals": 18}], "GFARM2": [{"network": "eth", "address": "******************************************", "name": "Gains V2", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Gains V2", "decimals": 18}], "SPONGE": [{"network": "eth", "address": "******************************************", "name": "SpongeBob", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Sponge", "decimals": 18}], "APW": [{"network": "eth", "address": "******************************************", "name": "APWine <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "AP<PERSON>ine <PERSON> (PoS)", "decimals": 18}], "BTCP": [{"network": "eth", "address": "******************************************", "name": "BitcoinPro", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "BitcoinPro", "decimals": 8}], "DCN": [{"network": "eth", "address": "******************************************", "name": "Compute Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Decentra Protocol Token", "decimals": 18}], "WPPC": [{"network": "eth", "address": "******************************************", "name": "WrappedPeercoin", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "WrappedPeercoin", "decimals": 6}], "FBX": [{"network": "eth", "address": "******************************************", "name": "FBX Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "FireBotToken", "decimals": 18}], "ROOBEE": [{"network": "eth", "address": "******************************************", "name": "ROOBEE", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "ROOBEE", "decimals": null}], "ETHV": [{"network": "eth", "address": "******************************************", "name": "ETH Volatility Index", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ETH Volatility Index", "decimals": 18}], "PAAL": [{"network": "eth", "address": "******************************************", "name": "PAAL AI", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PAAL AI", "decimals": 6}], "ARPA": [{"network": "eth", "address": "******************************************", "name": "ARPA Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ARPA Token (PoS)", "decimals": 18}], "JPYC": [{"network": "eth", "address": "******************************************", "name": "JPY Coin", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "JPY Coin", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "JPY Coin", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "JPY Coin (PoS)", "decimals": 18}], "FRY": [{"network": "eth", "address": "******************************************", "name": "Foundry Logistics Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Foundry Logistics Token", "decimals": 18}], "EROWAN": [{"network": "eth", "address": "******************************************", "name": "erowan", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (erowan) (PoS)", "decimals": 18}], "RADIO": [{"network": "eth", "address": "******************************************", "name": "RadioShack Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "RadioShack Token", "decimals": 18}], "DIMO": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "SOLX": [{"network": "eth", "address": "******************************************", "name": "SOLAXY", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "SOLAXY", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Solaxy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Solaxy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "SOLX SOLAXY", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Solaxy", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Solaxy", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "SolarX", "decimals": 18}], "JRT": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON> (PoS)", "decimals": 18}], "ASIA": [{"network": "eth", "address": "******************************************", "name": "ASIA COIN", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "ASIA", "decimals": 18}], "CIRUS": [{"network": "eth", "address": "******************************************", "name": "Cirus", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Cirus (PoS)", "decimals": 18}], "UFARM": [{"network": "eth", "address": "******************************************", "name": "UNIFARM Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "UNIFARM Token (PoS)", "decimals": 18}], "ASTRAFER": [{"network": "eth", "address": "******************************************", "name": "Astrafer", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Astrafer", "decimals": null}], "C3": [{"network": "eth", "address": "******************************************", "name": "CHARLI3", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "C3 Token", "decimals": 18}], "TIME": [{"network": "eth", "address": "******************************************", "name": "ChronoTech Token", "decimals": 8}, {"network": "eth", "address": "******************************************", "name": "T.I.M.E. Dividend", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Time Token", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "T.I.M.E. Dividend", "decimals": 18}], "ETHI": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON> Inu", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "ETHical Finance", "decimals": null}], "JM": [{"network": "eth", "address": "******************************************", "name": "JustMoney", "decimals": 8}, {"network": "polygon_pos", "address": "******************************************", "name": "JustMoney", "decimals": 8}], "FOMO": [{"network": "eth", "address": "******************************************", "name": "FOMO Network", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Aavegotchi FOMO", "decimals": 18}], "COMBO": [{"network": "eth", "address": "******************************************", "name": "Furucombo", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "decimals": 18}], "CCDAO": [{"network": "eth", "address": "******************************************", "name": "Cross-Chain DAO", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Cross-Chain DAO", "decimals": 18}], "CHI": [{"network": "eth", "address": "******************************************", "name": "CHI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Chi USD", "decimals": null}], "LAND": [{"network": "eth", "address": "******************************************", "name": "Ice Land", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Landshare Token", "decimals": 18}], "OOKI": [{"network": "eth", "address": "******************************************", "name": "<PERSON>oki <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "decimals": 18}], "EUROE": [{"network": "eth", "address": "******************************************", "name": "EUROe Stablecoin", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "EUROe Stablecoin", "decimals": 6}], "TAO": [{"network": "eth", "address": "******************************************", "name": "Bittensor", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Fool", "decimals": 18}], "PAT": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "PAT", "decimals": null}], "UERII": [{"network": "eth", "address": "******************************************", "name": "UERII", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "UERII (PoS)", "decimals": 18}], "BIKE": [{"network": "eth", "address": "******************************************", "name": "Bicycle by <PERSON>", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "Bike", "decimals": null}], "PI": [{"network": "eth", "address": "******************************************", "name": "Pi Network", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "Pi Futures", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Pi Network", "decimals": 18}], "USDY": [{"network": "eth", "address": "******************************************", "name": "Ondo U.S. Dollar Yield", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Stablecoin Yield", "decimals": null}], "GIV": [{"network": "eth", "address": "******************************************", "name": "GIVToken", "decimals": 8}, {"network": "optimism", "address": "******************************************", "name": "Giveth Token", "decimals": null}], "SWCH": [{"network": "eth", "address": "******************************************", "name": "Swiss<PERSON><PERSON>se", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Swiss<PERSON><PERSON>se <PERSON>", "decimals": 18}], "BEAT": [{"network": "eth", "address": "******************************************", "name": "Beat Matchmaker", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "METABEAT", "decimals": null}], "WOKE": [{"network": "eth", "address": "******************************************", "name": "WIKIPEDIA Google facebook OpenAI", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON>", "decimals": 18}], "NPT": [{"network": "eth", "address": "******************************************", "name": "Non Playable Trump", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "NEOPIN Token", "decimals": 18}], "RODO": [{"network": "eth", "address": "******************************************", "name": "RODO", "decimals": 2}, {"network": "polygon_pos", "address": "******************************************", "name": "RODO", "decimals": null}], "ASK": [{"network": "eth", "address": "******************************************", "name": "ask", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Permission Token", "decimals": 18}], "REMN": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "GOO": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "Goo", "decimals": null}], "AKT": [{"network": "eth", "address": "******************************************", "name": "Akash Network", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Akash Network (PoS)", "decimals": null}], "FAST": [{"network": "eth", "address": "******************************************", "name": "FASTFROG", "decimals": 14}, {"network": "polygon_pos", "address": "******************************************", "name": "Edge Video AI", "decimals": null}], "BLOCKS": [{"network": "eth", "address": "******************************************", "name": "BlockRiders", "decimals": 9}, {"network": "eth", "address": "******************************************", "name": "BLOCKS", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BLOCKS", "decimals": 18}], "VETH": [{"network": "eth", "address": "******************************************", "name": "Vitalik Elephant", "decimals": 9}, {"network": "optimism", "address": "******************************************", "name": "vETH", "decimals": null}], "HC": [{"network": "eth", "address": "******************************************", "name": "HC", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}], "DOGA": [{"network": "eth", "address": "******************************************", "name": "American Doge", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "DOGAMI", "decimals": 5}], "ELONINDEX": [{"network": "eth", "address": "******************************************", "name": "Dogelon Index", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Dogelon Index", "decimals": 18}], "ATOM": [{"network": "eth", "address": "******************************************", "name": "Cosmos Hub ATOM", "decimals": 6}, {"network": "polygon_pos", "address": "******************************************", "name": "Cosmos (PoS)", "decimals": 6}], "QI": [{"network": "eth", "address": "******************************************", "name": "Green Frog", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "DEATH": [{"network": "eth", "address": "******************************************", "name": "Death's Replicade", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "DEATH TOKEN", "decimals": 18}], "LIF3": [{"network": "eth", "address": "******************************************", "name": "LIF3", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "LIF3", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "LIF3", "decimals": 18}], "RING": [{"network": "eth", "address": "******************************************", "name": "Ring AI", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Darwinia Network Native Token", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "OneRing", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "OneRing", "decimals": null}], "ORB": [{"network": "eth", "address": "******************************************", "name": "Orb Protocol", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "OrbCity", "decimals": 18}], "GAIA": [{"network": "eth", "address": "******************************************", "name": "Gaia", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "GAIA Everworld", "decimals": 18}], "MASQ": [{"network": "eth", "address": "******************************************", "name": "MASQ", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "MASQ (PoS)", "decimals": 18}], "BTCBR": [{"network": "eth", "address": "******************************************", "name": "BitcoinBR", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "BitcoinBR | btcbr.info", "decimals": 18}], "OS": [{"network": "eth", "address": "******************************************", "name": "Ethereans", "decimals": 18}, {"network": "optimism", "address": "******************************************", "name": "Ethereans", "decimals": null}], "KTON": [{"network": "eth", "address": "******************************************", "name": "Darwinia Commitment Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Darwinia Commitment Token (PoS)", "decimals": 18}], "GM": [{"network": "eth", "address": "******************************************", "name": "GM", "decimals": 10}, {"network": "polygon_pos", "address": "******************************************", "name": "Guild Member Token", "decimals": 18}], "CASH": [{"network": "eth", "address": "******************************************", "name": "Cash", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "CASH", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyCash", "decimals": 18}], "UFI": [{"network": "eth", "address": "******************************************", "name": "PureFi <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "decimals": null}], "SHACK": [{"network": "eth", "address": "******************************************", "name": "Shack Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Shack Token", "decimals": null}], "KNG": [{"network": "eth", "address": "******************************************", "name": "Kanga Exchange Token", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Kanga Exchange Token (PoS)", "decimals": 18}], "TT": [{"network": "eth", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": 18}, {"network": "eth", "address": "******************************************", "name": "Tesla <PERSON>", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "TT", "decimals": 18}], "WEED": [{"network": "eth", "address": "******************************************", "name": "POTHEADS", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "PolyWeed Token", "decimals": 18}], "AF": [{"network": "eth", "address": "******************************************", "name": "America Fair", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "decimals": null}], "LINDA": [{"network": "eth", "address": "******************************************", "name": "<PERSON>", "decimals": 9}, {"network": "polygon_pos", "address": "******************************************", "name": "<PERSON>", "decimals": 18}], "BTS": [{"network": "eth", "address": "******************************************", "name": "BitSchilling", "decimals": 18}, {"network": "polygon_pos", "address": "******************************************", "name": "Bitcast", "decimals": 8}], "XMT": [{"network": "optimism", "address": "******************************************", "name": "MetalSwap", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "MetalSwap", "decimals": 18}], "WUSDR": [{"network": "optimism", "address": "******************************************", "name": "Wrapped USDR", "decimals": null}, {"network": "optimism", "address": "******************************************", "name": "Wrapped USDR", "decimals": null}, {"network": "polygon_pos", "address": "******************************************", "name": "Wrapped USDR", "decimals": 9}], "HND": [{"network": "optimism", "address": "******************************************", "name": "Hundred Finance", "decimals": null}, {"network": "polygon_pos", "address": "0xb4ab9700b9287b682634d4534193669a32c06b90", "name": "Tokyo Token", "decimals": 18}], "NEXT": [{"network": "optimism", "address": "0x58b9cb810a68a7f3e1e4f8cb45d1b9b3c79705e8", "name": "Connext", "decimals": null}, {"network": "polygon_pos", "address": "0x58b9cb810a68a7f3e1e4f8cb45d1b9b3c79705e8", "name": "Connext", "decimals": 18}], "XBURN": [{"network": "optimism", "address": "0x9d16374c01cf785b6db5b02a830e00c40c5381d8", "name": "XBURN", "decimals": null}, {"network": "polygon_pos", "address": "0xf6143c6134be3c3fd3431467d1252a2d18c89cde", "name": "XBURN", "decimals": 18}], "ECOIN": [{"network": "optimism", "address": "0x21aba8e16c7c60b4c168acec8c3e57924bffdbe8", "name": "Eaglecoin", "decimals": null}, {"network": "polygon_pos", "address": "0xb2d28599428c66f75fee3184aeda2eae62c3707c", "name": "eCoin.io", "decimals": null}]}