{"name": "web3-eth-ens", "version": "1.10.4", "description": "ENS support for web3.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-eth-ens", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib  types"}, "main": "lib/index.js", "dependencies": {"content-hash": "^2.5.2", "eth-ens-namehash": "2.0.8", "web3-core": "1.10.4", "web3-core-helpers": "1.10.4", "web3-core-promievent": "1.10.4", "web3-eth-abi": "1.10.4", "web3-eth-contract": "1.10.4", "web3-utils": "1.10.4"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.9.5", "web3-eth": "1.10.4"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}