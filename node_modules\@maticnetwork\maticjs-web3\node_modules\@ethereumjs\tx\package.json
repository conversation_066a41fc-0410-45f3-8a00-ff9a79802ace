{"name": "@ethereumjs/tx", "version": "3.5.2", "description": "A simple module for creating, manipulating and signing Ethereum transactions", "license": "MPL-2.0", "author": "mjbecze <<EMAIL>>", "keywords": ["ethereum", "transactions"], "files": ["dist", "dist.browser", "src"], "main": "dist/index.js", "types": "dist/index.d.ts", "browser": "dist.browser/index.js", "scripts": {"build": "npm run build:node && npm run build:browser", "build:node": "../../config/cli/ts-build.sh node", "build:browser": "../../config/cli/ts-build.sh browser", "prepublishOnly": "../../config/cli/prepublish.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "docs:build": "typedoc --options typedoc.js", "tsc": "../../config/cli/ts-compile.sh", "lint": "../../config/cli/lint.sh", "lint:fix": "../../config/cli/lint-fix.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser", "test:node": "tape -r ts-node/register ./test/index.ts", "test:browser": "karma start karma.conf.js", "examples": "ts-node ../../scripts/examples-runner.ts -- tx"}, "dependencies": {"@ethereumjs/common": "^2.6.4", "ethereumjs-util": "^7.1.5"}, "devDependencies": {"@types/minimist": "^1.2.0", "@types/node": "^16.11.7", "@types/tape": "^4.13.2", "eslint": "^6.8.0", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-tap": "^4.2.0", "karma-typescript": "^5.5.3", "minimist": "^1.2.0", "node-dir": "^0.1.16", "nyc": "^15.1.0", "prettier": "^2.0.5", "tape": "^5.3.1", "typedoc": "^0.22.4", "ts-node": "^10.2.1", "typescript": "^4.4.2"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "contributions": 22, "additions": 27562, "deletions": 42613, "hireable": true}], "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/tx#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+tx%22"}}