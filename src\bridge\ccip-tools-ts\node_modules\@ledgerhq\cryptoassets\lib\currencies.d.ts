import { CryptoCurrency, CryptoCurrencyId } from "@ledgerhq/types-cryptoassets";
export declare const cryptocurrenciesById: Record<CryptoCurrencyId, CryptoCurrency>;
/**
 *
 * @param {string} id
 * @param {CryptoCurrency} currency
 */
export declare function registerCryptoCurrency(currency: CryptoCurrency): void;
/**
 *
 * @param {*} withDevCrypto
 * @param {*} withTerminated
 */
export declare function listCryptoCurrencies(withDevCrypto?: boolean, withTerminated?: boolean): CryptoCurrency[];
/**
 *
 * @param {*} f
 */
export declare function findCryptoCurrency(f: (arg0: CryptoCurrency) => boolean): CryptoCurrency | null | undefined;
/**
 *
 * @param {*} scheme
 */
export declare function findCryptoCurrencyByScheme(scheme: string): CryptoCurrency | null | undefined;
/**
 *
 * @param {*} ticker
 */
export declare function findCryptoCurrencyByTicker(ticker: string): CryptoCurrency | null | undefined;
export declare function findCryptoCurrencyById(id: string): CryptoCurrency | undefined;
/**
 *
 * @param {*} keyword
 */
export declare const findCryptoCurrencyByKeyword: (keyword: string, tests?: string[]) => CryptoCurrency | null | undefined;
export declare const findCryptoCurrencyByManagerAppName: (managerAppName: string) => CryptoCurrency | null | undefined;
/**
 *
 * @param {*} id
 */
export declare const hasCryptoCurrencyId: (id: string) => boolean;
export declare function getCryptoCurrencyById(id: string): CryptoCurrency;
//# sourceMappingURL=currencies.d.ts.map