@echo off
REM CCIP Tools Windows Batch Script
REM 简化CCIP命令行工具的使用

setlocal enabledelayedexpansion

REM 设置默认配置文件路径
set "DEFAULT_CONFIG=..\..\..\config\rpc.yaml"
set "CCIP_SCRIPT=.\src\index.ts"

REM 显示当前目录用于调试
echo Current directory: %CD%

REM 检查Node.js是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18+ first.
    exit /b 1
)

REM 检查npx是否可用
where npx >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npx not found. Please ensure npm is properly installed.
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "%DEFAULT_CONFIG%" (
    echo ❌ Configuration file not found: %DEFAULT_CONFIG%
    echo Please ensure config\rpc.yaml exists in your project root.
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "%CCIP_SCRIPT%" (
    echo ❌ CCIP script not found: %CCIP_SCRIPT%
    echo Please ensure you're running this from the ccip-tools-ts directory.
    exit /b 1
)

REM 如果没有参数，显示帮助
if "%~1"=="" (
    echo 🚀 CCIP Tools - Windows Command Helper
    echo =====================================
    echo.
    echo Usage: ccip.bat ^<command^> [options]
    echo.
    echo Common commands:
    echo   list-networks                    - List all supported networks
    echo   show ^<tx_hash^>                   - Show CCIP message info
    echo   send ^<source^> ^<router^> ^<dest^>    - Send cross-chain message
    echo   getSupportedTokens ^<network^>    - Get supported tokens for network
    echo   manualExec ^<tx_hash^>            - Manually execute pending message
    echo.
    echo Network examples:
    echo   ethereum, eth, mainnet           - Ethereum Mainnet ^(Chain ID: 1^)
    echo   polygon, matic                   - Polygon Mainnet ^(Chain ID: 137^)
    echo   bsc, bnb, binance               - BSC Mainnet ^(Chain ID: 56^)
    echo   avalanche, avax                 - Avalanche Mainnet ^(Chain ID: 43114^)
    echo   sepolia, eth-sepolia            - Ethereum Sepolia Testnet
    echo   fuji, avax-fuji                 - Avalanche Fuji Testnet
    echo.
    echo Examples:
    echo   ccip.bat list-networks
    echo   ccip.bat show 0x1234...
    echo   ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
    echo   ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --data "Hello World"
    echo   ccip.bat getSupportedTokens ethereum
    echo.
    echo For more help: ccip.bat --help
    goto :eof
)

REM 特殊命令处理
if "%~1"=="help" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --help
    goto :eof
)

if "%~1"=="--help" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --help
    goto :eof
)

if "%~1"=="version" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --version
    goto :eof
)

if "%~1"=="--version" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --version
    goto :eof
)

REM 构建命令行参数
set "ARGS="
set "SKIP_NEXT="

:parse_args
if "%~1"=="" goto :execute_command

REM 跳过已处理的参数
if defined SKIP_NEXT (
    set "SKIP_NEXT="
    shift
    goto :parse_args
)

REM 检查是否已经包含yaml-config参数
echo %* | findstr /i "yaml-config" >nul
if %errorlevel% equ 0 (
    REM 用户已经指定了yaml-config，不添加默认的
    set "ARGS=!ARGS! %~1"
) else (
    REM 添加参数到命令行
    set "ARGS=!ARGS! %~1"
)

shift
goto :parse_args

:execute_command
REM 检查是否需要添加默认配置
echo %ARGS% | findstr /i "yaml-config" >nul
if %errorlevel% neq 0 (
    set "ARGS=!ARGS! --yaml-config "%DEFAULT_CONFIG%""
)

REM 执行命令
echo 🚀 Executing: npx tsx "%CCIP_SCRIPT%" !ARGS!
echo.
npx tsx "%CCIP_SCRIPT%" !ARGS!

REM 检查执行结果
if %errorlevel% neq 0 (
    echo.
    echo ❌ Command failed with exit code %errorlevel%
    echo.
    echo 💡 Tips:
    echo   - Use "ccip.bat list-networks" to see supported networks
    echo   - Check if your RPC configuration is correct
    echo   - Ensure all addresses are valid and properly formatted
    echo   - Use --verbose flag for detailed logs
    exit /b %errorlevel%
) else (
    echo.
    echo ✅ Command completed successfully!
)

endlocal
