@echo off
REM CCIP Tools Windows Batch Script
REM Simplify CCIP command line tool usage

setlocal enabledelayedexpansion

REM Set default config file path
set "DEFAULT_CONFIG=..\..\..\config\rpc.yaml"
set "CCIP_SCRIPT=.\src\index.ts"

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Node.js not found. Please install Node.js 18+ first.
    exit /b 1
)

REM Check if npx is available
where npx >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: npx not found. Please ensure npm is properly installed.
    exit /b 1
)

REM Check if config file exists
if not exist "%DEFAULT_CONFIG%" (
    echo Error: Configuration file not found: %DEFAULT_CONFIG%
    echo Please ensure config\rpc.yaml exists in your project root.
    exit /b 1
)

REM Check if script file exists
if not exist "%CCIP_SCRIPT%" (
    echo Error: CCIP script not found: %CCIP_SCRIPT%
    echo Please ensure you're running this from the ccip-tools-ts directory.
    exit /b 1
)

REM If no parameters, show help
if "%~1"=="" (
    echo CCIP Tools - Windows Command Helper
    echo ===================================
    echo.
    echo Usage: ccip.bat ^<command^> [options]
    echo.
    echo Common commands:
    echo   list-networks                    - List all supported networks
    echo   show ^<tx_hash^>                   - Show CCIP message info
    echo   send ^<source^> ^<router^> ^<dest^>    - Send cross-chain message
    echo   getSupportedTokens ^<network^>    - Get supported tokens for network
    echo   manualExec ^<tx_hash^>            - Manually execute pending message
    echo.
    echo Network examples:
    echo   ethereum, eth, mainnet           - Ethereum Mainnet ^(Chain ID: 1^)
    echo   polygon, matic                   - Polygon Mainnet ^(Chain ID: 137^)
    echo   bsc, bnb, binance               - BSC Mainnet ^(Chain ID: 56^)
    echo   avalanche, avax                 - Avalanche Mainnet ^(Chain ID: 43114^)
    echo   sepolia, eth-sepolia            - Ethereum Sepolia Testnet
    echo   fuji, avax-fuji                 - Avalanche Fuji Testnet
    echo.
    echo Examples:
    echo   ccip.bat list-networks
    echo   ccip.bat show 0x1234...
    echo   ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
    echo   ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --data "Hello World"
    echo   ccip.bat getSupportedTokens ethereum
    echo.
    echo For more help: ccip.bat --help
    goto :eof
)

REM Handle special commands
if "%~1"=="help" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --help
    goto :eof
)

if "%~1"=="--help" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --help
    goto :eof
)

if "%~1"=="version" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --version
    goto :eof
)

if "%~1"=="--version" (
    npx tsx "%CCIP_SCRIPT%" --yaml-config "%DEFAULT_CONFIG%" --version
    goto :eof
)

REM Build command line arguments
set "ARGS="
set "SKIP_NEXT="

:parse_args
if "%~1"=="" goto :execute_command

REM Skip already processed arguments
if defined SKIP_NEXT (
    set "SKIP_NEXT="
    shift
    goto :parse_args
)

REM Check if yaml-config parameter is already included
echo %* | findstr /i "yaml-config" >nul
if %errorlevel% equ 0 (
    REM User already specified yaml-config, don't add default
    set "ARGS=!ARGS! %~1"
) else (
    REM Add parameter to command line
    set "ARGS=!ARGS! %~1"
)

shift
goto :parse_args

:execute_command
REM Check if we need to add default config
echo %ARGS% | findstr /i "yaml-config" >nul
if %errorlevel% neq 0 (
    set "ARGS=!ARGS! --yaml-config "%DEFAULT_CONFIG%""
)

REM Execute command
echo Executing: npx tsx "%CCIP_SCRIPT%" !ARGS!
echo.
npx tsx "%CCIP_SCRIPT%" !ARGS!

REM Check execution result
if %errorlevel% neq 0 (
    echo.
    echo Command failed with exit code %errorlevel%
    echo.
    echo Tips:
    echo   - Use "ccip.bat list-networks" to see supported networks
    echo   - Check if your RPC configuration is correct
    echo   - Ensure all addresses are valid and properly formatted
    echo   - Use --verbose flag for detailed logs
    exit /b %errorlevel%
) else (
    echo.
    echo Command completed successfully!
)

endlocal
