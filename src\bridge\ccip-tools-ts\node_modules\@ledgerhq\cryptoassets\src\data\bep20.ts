export type BEP20Token = [
  string, // parent currency id
  string, // token
  string, // ticker
  number, // precision
  string, // name
  string, // ledgerSignature
  string, // contract address
  boolean, // disabled counter value
  boolean, // delisted
  (string | null)?, // legacy
];

import tokens from "./bep20.json";

export { default as hash } from "./bep20-hash.json";

export default tokens as BEP20Token[];
