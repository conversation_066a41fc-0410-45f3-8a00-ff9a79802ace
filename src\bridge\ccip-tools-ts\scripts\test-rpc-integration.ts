#!/usr/bin/env -S npx tsx

/**
 * 测试RPC整合脚本
 * 验证RPC配置是否正确加载和工作
 */

import { resolve } from 'node:path'
import { existsSync } from 'node:fs'
import { Providers } from '../src/providers.ts'
import { 
  initializeRpcManager, 
  loadRpcConfig, 
  getAllRpcEndpoints,
  NETWORK_CHAIN_ID_MAP 
} from '../src/config/rpc-integration.ts'

async function testRpcIntegration() {
  console.log('🧪 Testing RPC Integration')
  console.log('==========================')

  const configPath = resolve(process.cwd(), '../../../config/rpc.yaml')
  console.log(`📁 Config path: ${configPath}`)
  
  // 1. 检查配置文件是否存在
  if (!existsSync(configPath)) {
    console.error(`❌ Config file not found: ${configPath}`)
    console.log('Please ensure the config/rpc.yaml file exists in the project root.')
    process.exit(1)
  }
  
  console.log('✅ Config file found')

  try {
    // 2. 测试加载RPC配置
    console.log('\n📋 Loading RPC configuration...')
    const chainRpcs = await loadRpcConfig(configPath)
    
    if (chainRpcs.size === 0) {
      console.warn('⚠️  No RPC configurations loaded')
      return
    }
    
    console.log(`✅ Loaded configurations for ${chainRpcs.size} networks`)
    
    // 3. 显示配置详情
    console.log('\n🌐 Network configurations:')
    for (const [chainId, rpcs] of chainRpcs.entries()) {
      const networkName = Object.entries(NETWORK_CHAIN_ID_MAP)
        .find(([, id]) => id === chainId)?.[0] || 'Unknown'
      console.log(`  ${networkName} (${chainId}): ${rpcs.length} RPC(s)`)
      rpcs.forEach((rpc, index) => {
        console.log(`    ${index + 1}. ${rpc}`)
      })
    }
    
    // 4. 测试RPC管理器
    console.log('\n🔄 Testing RPC rotation manager...')
    const rpcManager = await initializeRpcManager(configPath)
    
    // 测试几个主要网络的RPC轮换
    const testChains = [1, 137, 56, 43114] // Ethereum, Polygon, BSC, Avalanche
    
    for (const chainId of testChains) {
      const availableRpcs = rpcManager.getAvailableRpcsForChain(chainId)
      if (availableRpcs.length > 0) {
        console.log(`\n  Chain ${chainId}:`)
        console.log(`    Available RPCs: ${availableRpcs.length}`)
        
        // 测试获取下一个RPC
        for (let i = 0; i < Math.min(3, availableRpcs.length); i++) {
          const nextRpc = rpcManager.getNextRpcForChain(chainId)
          console.log(`    Next RPC ${i + 1}: ${nextRpc}`)
        }
        
        // 测试失败处理
        if (availableRpcs.length > 1) {
          const testRpc = availableRpcs[0]
          console.log(`    Testing failure handling for: ${testRpc}`)
          rpcManager.markRpcAsFailed(testRpc)
          
          const nextAfterFailure = rpcManager.getNextRpcForChain(chainId)
          console.log(`    Next RPC after marking failure: ${nextAfterFailure}`)
          
          // 恢复RPC
          rpcManager.markRpcAsSuccess(testRpc)
        }
      }
    }
    
    // 5. 测试Providers类
    console.log('\n🔗 Testing Providers class with YAML config...')
    const providers = new Providers({ 'yaml-config': configPath })
    
    console.log('⏳ Waiting for providers to initialize...')
    
    // 设置超时
    const timeout = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout waiting for providers')), 30000)
    })
    
    try {
      await Promise.race([providers.completed, timeout])
      console.log('✅ Providers initialized successfully!')
      
      // 测试获取特定链的provider
      const testChainIds = [1, 137, 43113] // 测试几个主要网络
      
      for (const chainId of testChainIds) {
        try {
          const provider = await providers.forChainId(chainId)
          const network = await provider.getNetwork()
          console.log(`✅ Provider for chain ${chainId}: Connected to network ${network.chainId}`)
        } catch (error) {
          console.log(`⚠️  No provider available for chain ${chainId}: ${error}`)
        }
      }
      
    } catch (error) {
      console.error(`❌ Provider initialization failed: ${error}`)
    } finally {
      providers.destroy()
    }
    
    console.log('\n✨ RPC integration test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testRpcIntegration().catch(console.error)
}

export { testRpcIntegration }
