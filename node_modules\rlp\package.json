{"name": "rlp", "version": "3.0.0", "description": "Recursive Length Prefix Encoding Module", "license": "MPL-2.0", "keywords": ["rlp", "ethereum"], "files": ["dist", "bin", "src"], "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"rlp": "bin/rlp"}, "scripts": {"build": "../../config/cli/ts-build.sh node", "prepublishOnly": "../../config/cli/prepublish.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "tsc": "../../config/cli/ts-compile.sh", "lint": "../../config/cli/lint.sh", "lint:fix": "../../config/cli/lint-fix.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser", "test:node": "npm run tape -- test/*.spec.ts", "test:browser": "karma start karma.conf.js"}, "devDependencies": {"@types/node": "^16.11.7", "@types/tape": "^4.13.2", "karma": "^6.3.4", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.1", "karma-tap": "^4.2.0", "karma-typescript": "^5.5.3", "nyc": "^15.1.0", "prettier": "^2.4.1", "tape": "^5.3.1", "ts-node": "^10.2.1", "typescript": "^4.4.2"}, "author": {"name": "martin be<PERSON>e", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <Holger.<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/rlp#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+rlp%22"}}