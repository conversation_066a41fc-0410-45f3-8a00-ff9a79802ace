{"name": "web3", "version": "1.10.4", "description": "Ethereum JavaScript API", "repository": "https://github.com/ethereum/web3.js", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "main": "lib/index.js", "browser": "dist/web3.min.js", "bugs": {"url": "https://github.com/ethereum/web3.js/issues"}, "keywords": ["Ethereum", "JavaScript", "API"], "author": "ethereum.org", "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib types", "postinstall": "echo \"Web3.js 4.x alpha has been released for early testing and feedback. Checkout doc at https://docs.web3js.org/ \""}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://frozeman.de"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/debris"}, {"name": "<PERSON>", "url": "https://github.com/cubedro"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://gavwood.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/obscuren"}], "dependencies": {"web3-bzz": "1.10.4", "web3-core": "1.10.4", "web3-eth": "1.10.4", "web3-eth-personal": "1.10.4", "web3-net": "1.10.4", "web3-shh": "1.10.4", "web3-utils": "1.10.4"}, "devDependencies": {"@types/node": "^12.12.6", "dtslint": "^3.4.1", "typescript": "4.9.5", "web3-core-helpers": "1.10.4"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}