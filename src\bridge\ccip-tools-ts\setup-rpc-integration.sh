#!/bin/bash

# CCIP Tools RPC Integration Setup Script
# 用于快速设置和测试RPC整合功能

set -e

echo "🚀 CCIP Tools RPC Integration Setup"
echo "===================================="

# 检查Node.js版本
echo "📋 Checking Node.js version..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please upgrade to Node.js 18+."
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# 检查配置文件
echo ""
echo "📁 Checking configuration files..."
CONFIG_PATH="../../../config/rpc.yaml"

if [ ! -f "$CONFIG_PATH" ]; then
    echo "❌ RPC configuration file not found: $CONFIG_PATH"
    echo ""
    echo "Please ensure you have the config/rpc.yaml file in your project root."
    echo "The file should contain RPC endpoints for different networks."
    echo ""
    echo "Example structure:"
    echo "rpc:"
    echo "  ethereum:"
    echo "    rpc_url: \"https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY\""
    echo "    backup_rpc_urls:"
    echo "      - \"https://mainnet.infura.io/v3/YOUR_KEY\""
    echo ""
    exit 1
fi

echo "✅ Configuration file found: $CONFIG_PATH"

# 安装依赖
echo ""
echo "📦 Installing dependencies..."
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "✅ Dependencies already installed"
fi

# 运行类型检查
echo ""
echo "🔍 Running type check..."
npm run typecheck

# 测试RPC整合
echo ""
echo "🧪 Testing RPC integration..."
npm run test:rpc

# 运行示例
echo ""
echo "🎯 Running integration example..."
npm run example:rpc

echo ""
echo "✨ Setup completed successfully!"
echo ""
echo "🎉 You can now use CCIP tools with your RPC configuration:"
echo ""
echo "# Show CCIP message info"
echo "./src/index.ts --yaml-config $CONFIG_PATH show <tx_hash>"
echo ""
echo "# Send cross-chain message"
echo "./src/index.ts --yaml-config $CONFIG_PATH send <source_chain> <router> <dest_chain> --receiver <address>"
echo ""
echo "# Get supported tokens"
echo "./src/index.ts --yaml-config $CONFIG_PATH getSupportedTokens <chain_id>"
echo ""
echo "# Manual execution"
echo "./src/index.ts --yaml-config $CONFIG_PATH manualExec <tx_hash>"
echo ""
echo "For more information, see RPC_INTEGRATION.md"
