# CCIP Tools PowerShell Script
# 简化CCIP命令行工具的使用

param(
    [Parameter(Position=0)]
    [string]$Command,

    [Parameter(Position=1, ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

# 设置执行策略以允许脚本运行
if ((Get-ExecutionPolicy) -eq 'Restricted') {
    Write-Host "⚠️  PowerShell execution policy is restricted. Attempting to set for current session..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process -Force
        Write-Host "✅ Execution policy set for current session" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set execution policy. Please run as administrator or use cmd instead." -ForegroundColor Red
        exit 1
    }
}

# 设置默认配置文件路径
$DefaultConfig = "..\..\..\config\rpc.yaml"
$CCIPScript = ".\src\index.ts"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 检查Node.js是否安装
function Test-NodeJS {
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
            if ($versionNumber -lt 18) {
                Write-ColorOutput "❌ Node.js version $nodeVersion is too old. Please upgrade to Node.js 18+." "Red"
                exit 1
            }
            return $true
        }
    } catch {
        Write-ColorOutput "❌ Node.js not found. Please install Node.js 18+ first." "Red"
        exit 1
    }
    return $false
}

# 检查文件是否存在
function Test-Files {
    if (-not (Test-Path $DefaultConfig)) {
        Write-ColorOutput "❌ Configuration file not found: $DefaultConfig" "Red"
        Write-ColorOutput "Please ensure config\rpc.yaml exists in your project root." "Yellow"
        exit 1
    }
    
    if (-not (Test-Path $CCIPScript)) {
        Write-ColorOutput "❌ CCIP script not found: $CCIPScript" "Red"
        Write-ColorOutput "Please ensure you're running this from the ccip-tools-ts directory." "Yellow"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-ColorOutput "🚀 CCIP Tools - Windows PowerShell Helper" "Cyan"
    Write-ColorOutput "=========================================" "Cyan"
    Write-Host ""
    Write-ColorOutput "Usage: .\ccip.ps1 <command> [options]" "White"
    Write-Host ""
    Write-ColorOutput "Common commands:" "Green"
    Write-Host "  list-networks                    - List all supported networks"
    Write-Host "  show <tx_hash>                   - Show CCIP message info"
    Write-Host "  send <source> <router> <dest>    - Send cross-chain message"
    Write-Host "  getSupportedTokens <network>     - Get supported tokens for network"
    Write-Host "  manualExec <tx_hash>             - Manually execute pending message"
    Write-Host ""
    Write-ColorOutput "Network examples:" "Green"
    Write-Host "  ethereum, eth, mainnet           - Ethereum Mainnet (Chain ID: 1)"
    Write-Host "  polygon, matic                   - Polygon Mainnet (Chain ID: 137)"
    Write-Host "  bsc, bnb, binance               - BSC Mainnet (Chain ID: 56)"
    Write-Host "  avalanche, avax                 - Avalanche Mainnet (Chain ID: 43114)"
    Write-Host "  sepolia, eth-sepolia            - Ethereum Sepolia Testnet"
    Write-Host "  fuji, avax-fuji                 - Avalanche Fuji Testnet"
    Write-Host ""
    Write-ColorOutput "Examples:" "Green"
    Write-Host "  .\ccip.ps1 list-networks"
    Write-Host "  .\ccip.ps1 show 0x1234..."
    Write-Host "  .\ccip.ps1 send ethereum 0xRouter polygon --receiver 0xAddress"
    Write-Host "  .\ccip.ps1 send sepolia 0xRouter fuji --receiver 0xAddress --data 'Hello World'"
    Write-Host "  .\ccip.ps1 getSupportedTokens ethereum"
    Write-Host ""
    Write-ColorOutput "For more help: .\ccip.ps1 --help" "Yellow"
}

# 主函数
function Main {
    # 检查环境
    Test-NodeJS
    Test-Files
    
    # 如果没有参数，显示帮助
    if (-not $Command) {
        Show-Help
        return
    }
    
    # 特殊命令处理
    switch ($Command.ToLower()) {
        "help" { 
            & npx tsx $CCIPScript --yaml-config $DefaultConfig --help
            return
        }
        "--help" { 
            & npx tsx $CCIPScript --yaml-config $DefaultConfig --help
            return
        }
        "version" { 
            & npx tsx $CCIPScript --yaml-config $DefaultConfig --version
            return
        }
        "--version" { 
            & npx tsx $CCIPScript --yaml-config $DefaultConfig --version
            return
        }
    }
    
    # 构建参数列表
    $AllArgs = @($Command)
    if ($Arguments) {
        $AllArgs += $Arguments
    }
    
    # 检查是否已经包含yaml-config参数
    $hasYamlConfig = $false
    foreach ($arg in $AllArgs) {
        if ($arg -like "*yaml-config*") {
            $hasYamlConfig = $true
            break
        }
    }
    
    # 如果没有yaml-config参数，添加默认配置
    if (-not $hasYamlConfig) {
        $AllArgs += "--yaml-config"
        $AllArgs += $DefaultConfig
    }
    
    # 执行命令
    Write-ColorOutput "🚀 Executing: npx tsx $CCIPScript $($AllArgs -join ' ')" "Cyan"
    Write-Host ""
    
    try {
        & npx tsx $CCIPScript @AllArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-ColorOutput "✅ Command completed successfully!" "Green"
        } else {
            Write-Host ""
            Write-ColorOutput "❌ Command failed with exit code $LASTEXITCODE" "Red"
            Write-Host ""
            Write-ColorOutput "💡 Tips:" "Yellow"
            Write-Host "  - Use '.\ccip.ps1 list-networks' to see supported networks"
            Write-Host "  - Check if your RPC configuration is correct"
            Write-Host "  - Ensure all addresses are valid and properly formatted"
            Write-Host "  - Use --verbose flag for detailed logs"
            exit $LASTEXITCODE
        }
    } catch {
        Write-ColorOutput "❌ Error executing command: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# 运行主函数
Main
