{"version": 3, "file": "tokens.test.js", "sourceRoot": "", "sources": ["../src/tokens.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AACrD,OAAO,EACL,SAAS,EACT,YAAY,EACZ,UAAU,EACV,eAAe,EACf,aAAa,EACb,2BAA2B,EAC3B,eAAe,GAChB,MAAM,UAAU,CAAC;AAGlB,MAAM,aAAa,GAAiB;IAClC;QACE,UAAU;QACV,IAAI;QACJ,KAAK;QACL,CAAC;QACD,IAAI;QACJ,8IAA8I;QAC9I,4CAA4C;QAC5C,KAAK;QACL,KAAK;KACN;IACD;QACE,UAAU;QACV,UAAU;QACV,MAAM;QACN,EAAE;QACF,UAAU;QACV,gJAAgJ;QAChJ,4CAA4C;QAC5C,KAAK;QACL,KAAK;KACN;IACD;QACE,UAAU;QACV,MAAM;QACN,MAAM;QACN,CAAC;QACD,MAAM;QACN,8IAA8I;QAC9I,4CAA4C;QAC5C,KAAK;QACL,IAAI;KACL;IACD;QACE,UAAU;QACV,UAAU;QACV,MAAM;QACN,EAAE;QACF,UAAU;QACV,8IAA8I;QAC9I,4CAA4C;QAC5C,KAAK;QACL,IAAI;KACL;CACF,CAAC;AAEF,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAE3D,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,UAAU,CAAC,GAAG,EAAE;QACd,eAAe,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,UAAU,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3C,MAAM,WAAW,GAAiB;gBAChC;oBACE,UAAU;oBACV,UAAU;oBACV,MAAM;oBACN,EAAE;oBACF,UAAU;oBACV,gJAAgJ;oBAChJ,4CAA4C;oBAC5C,KAAK;oBACL,KAAK;iBACN;aACF,CAAC;YACF,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAClE,IAAI,CAAC,aAAa;gBAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC;YAEvD,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YACzC,MAAM,gBAAgB,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YACrE,IAAI,CAAC,gBAAgB;gBAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3C,MAAM,WAAW,GAAiB;gBAChC;oBACE,UAAU;oBACV,UAAU;oBACV,MAAM;oBACN,EAAE;oBACF,UAAU;oBACV,gJAAgJ;oBAChJ,4CAA4C;oBAC5C,KAAK;oBACL,IAAI;iBACL;aACF,CAAC;YACF,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC1B,MAAM,CAAC,UAAU,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE1D,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}