# web3

##### [Web3.js 4.x][4x-release] has been released. Checkout 4.x [API documentation and migration guide][4xdoc] for testing, early feedback and contributions.

[![NPM Package][npm-image]][npm-url]

This is the main package of [web3.js][repo].

Please read the main [README][repo-readme] and [documentation][docs] for more.

## Installation

You can install the package either using [NPM](https://www.npmjs.com/package/web3) or using [Yarn](https://yarnpkg.com/package/web3)

### Using NPM

```bash
npm install web3
```

### Using Yarn

```bash
yarn add web3
```

## Types

All the TypeScript typings are placed in the `types` folder.

[docs]: http://web3js.readthedocs.io/en/1.0/
[repo]: https://github.com/ethereum/web3.js
[repo-readme]: https://github.com/ethereum/web3.js/blob/1.x/README.md
[npm-image]: https://img.shields.io/npm/v/web3.svg
[npm-url]: https://npmjs.org/package/web3
[4x-release]: https://github.com/ChainSafe/web3.js/releases/tag/v4.0.1-rc.1
[4xdoc]: https://docs.web3js.org/
