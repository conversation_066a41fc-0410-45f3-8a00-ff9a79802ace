# Test CCIP functionality
param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

Write-Host "CCIP Test Script" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
Write-Host ""

# Check current directory
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check if Node.js is available
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Node.js not found" -ForegroundColor Red
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: npm not found" -ForegroundColor Red
    exit 1
}

# Check if tsx is available
try {
    $tsxVersion = npx tsx --version
    Write-Host "tsx version: $tsxVersion" -ForegroundColor Green
} catch {
    Write-Host "Warning: tsx not available, trying to install..." -ForegroundColor Yellow
    try {
        npm install tsx
        Write-Host "tsx installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "Error: Failed to install tsx" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""

# Check files
$configFile = "..\..\..\config\rpc.yaml"
$scriptFile = ".\src\index.ts"

if (Test-Path $configFile) {
    Write-Host "Config file found: $configFile" -ForegroundColor Green
} else {
    Write-Host "Warning: Config file not found: $configFile" -ForegroundColor Yellow
}

if (Test-Path $scriptFile) {
    Write-Host "Script file found: $scriptFile" -ForegroundColor Green
} else {
    Write-Host "Error: Script file not found: $scriptFile" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test basic command
Write-Host "Testing basic command..." -ForegroundColor Cyan
try {
    Write-Host "Command: npx tsx $scriptFile --version" -ForegroundColor Gray
    & npx tsx $scriptFile --version
    Write-Host "Basic command test passed!" -ForegroundColor Green
} catch {
    Write-Host "Error in basic command test: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "All tests completed!" -ForegroundColor Green
