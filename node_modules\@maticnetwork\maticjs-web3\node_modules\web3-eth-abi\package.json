{"name": "web3-eth-abi", "version": "1.10.4", "description": "Web3 module encode and decode EVM in/output.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-eth-abi", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib types"}, "main": "lib/index.js", "dependencies": {"@ethersproject/abi": "^5.6.3", "web3-utils": "1.10.4"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.9.5"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}