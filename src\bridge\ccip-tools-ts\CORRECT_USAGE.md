# CCIP Tools - 正确使用方法

## 🚨 重要发现

经过测试发现，原始的CCIP工具命令结构与我们预期的不同：

### ❌ 错误的理解
我们以为 `getSupportedTokens` 只需要一个网络参数，但实际上它需要3个参数：
- `source` (源网络)
- `router` (路由器地址)  
- `dest` (目标网络)

### ✅ 正确的命令格式

#### 1. 查看支持的网络
```powershell
npx tsx src\index.ts list-networks
```

#### 2. 查看支持的代币 (需要路由器地址)
```powershell
# 格式: getSupportedTokens <source> <router> <dest>
npx tsx src\index.ts getSupportedTokens ethereum ****************************************** polygon --yaml-config ..\..\..\config\rpc.yaml

# 测试网示例
npx tsx src\index.ts getSupportedTokens sepolia ****************************************** fuji --yaml-config ..\..\..\config\rpc.yaml
```

#### 3. 发送跨链消息
```powershell
npx tsx src\index.ts send ethereum ****************************************** polygon --receiver 0xYourAddress --yaml-config ..\..\..\config\rpc.yaml
```

#### 4. 查看消息状态
```powershell
npx tsx src\index.ts show 0xTransactionHash --yaml-config ..\..\..\config\rpc.yaml
```

#### 5. 手动执行消息
```powershell
npx tsx src\index.ts manualExec 0xTransactionHash --yaml-config ..\..\..\config\rpc.yaml
```

#### 6. 估算Gas费用
```powershell
npx tsx src\index.ts estimateGas ethereum ****************************************** polygon --receiver 0xYourAddress --yaml-config ..\..\..\config\rpc.yaml
```

#### 7. 查看通道信息
```powershell
npx tsx src\index.ts lane ethereum ****************************************** polygon --yaml-config ..\..\..\config\rpc.yaml
```

## 🌐 网络名称支持

### ✅ 已实现的功能
- 支持友好网络名称：`ethereum`, `polygon`, `sepolia`, `fuji` 等
- 大小写不敏感
- 自动验证和错误建议
- 向后兼容链ID

### 📋 支持的网络名称
| 友好名称 | 链ID | 类型 |
|---------|------|------|
| `ethereum`, `eth`, `mainnet` | 1 | 主网 |
| `polygon`, `matic` | 137 | 主网 |
| `bsc`, `bnb`, `binance` | 56 | 主网 |
| `avalanche`, `avax` | 43114 | 主网 |
| `arbitrum`, `arb` | 42161 | 主网 |
| `optimism`, `op` | 10 | 主网 |
| `base` | 8453 | 主网 |
| `sepolia`, `eth-sepolia` | 11155111 | 测试网 |
| `fuji`, `avax-fuji` | 43113 | 测试网 |
| `amoy`, `polygon-amoy` | 80002 | 测试网 |

## 🔧 常用的CCIP路由器地址

### 主网路由器
```
Ethereum: ******************************************
Polygon: ******************************************
BSC: ******************************************
Avalanche: ******************************************
Arbitrum: ******************************************
Optimism: ******************************************
Base: ******************************************
```

### 测试网路由器
```
Sepolia: ******************************************
Fuji: ******************************************
Amoy: ******************************************
```

## 📝 实际使用示例

### 示例1: 查看Ethereum到Polygon的支持代币
```powershell
npx tsx src\index.ts getSupportedTokens ethereum ****************************************** polygon --yaml-config ..\..\..\config\rpc.yaml
```

### 示例2: 从Sepolia发送消息到Fuji
```powershell
npx tsx src\index.ts send sepolia ****************************************** fuji --receiver 0xYourAddress --data "Hello Cross-Chain" --yaml-config ..\..\..\config\rpc.yaml
```

### 示例3: 查看所有支持的网络
```powershell
npx tsx src\index.ts list-networks
```

### 示例4: 发送代币跨链
```powershell
npx tsx src\index.ts send ethereum ****************************************** polygon --receiver 0xYourAddress --transfer-tokens 0xTokenAddress=1.5 --yaml-config ..\..\..\config\rpc.yaml
```

## 🛠️ 故障排除

### 问题1: 路径错误
```
ERR_MODULE_NOT_FOUND: Cannot resolve module
```
**解决**: 确保在正确的目录中运行命令：
```powershell
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts
```

### 问题2: 缺少路由器地址
```
Not enough non-option arguments: got 1, need at least 3
```
**解决**: 提供完整的参数：source、router、dest

### 问题3: 网络名称无效
```
Invalid source network: "etherem"
```
**解决**: 使用 `list-networks` 查看正确的网络名称

## 💡 最佳实践

1. **总是使用完整路径**: 确保在ccip-tools-ts目录中运行
2. **包含配置文件**: 添加 `--yaml-config ..\..\..\config\rpc.yaml`
3. **使用正确的路由器地址**: 参考上面的路由器地址列表
4. **先测试网络**: 使用 `list-networks` 验证网络名称
5. **使用测试网**: 在主网操作前先在测试网验证

## 🎯 快速验证

要验证网络名称功能：

```powershell
# 1. 进入正确目录
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts

# 2. 测试网络列表
npx tsx src\index.ts list-networks

# 3. 测试网络名称解析 (使用测试网)
npx tsx src\index.ts getSupportedTokens sepolia ****************************************** fuji --yaml-config ..\..\..\config\rpc.yaml

# 如果这些命令成功执行，说明网络名称功能正常工作！
```

现在您知道了正确的使用方法！网络名称功能已经实现，只是命令格式与预期略有不同。
