# CCIP Tools - Windows 使用示例

## 🚀 解决PowerShell问题的正确方法

### 问题
```powershell
PS> ccip.bat getSupportedTokens ethereum
ccip.bat : 无法将"ccip.bat"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```

### 解决方案

#### ✅ 方法1: 使用 `.\` 前缀 (推荐)
```powershell
# 正确的PowerShell语法
.\ccip.bat getSupportedTokens ethereum
.\ccip.bat list-networks
.\ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
```

#### ✅ 方法2: 使用PowerShell脚本
```powershell
.\ccip.ps1 getSupportedTokens ethereum
.\ccip.ps1 list-networks
.\ccip.ps1 send ethereum 0xRouter polygon --receiver 0xAddress
```

#### ✅ 方法3: 使用命令提示符 (CMD)
```cmd
REM 在CMD中可以直接使用，无需 .\ 前缀
ccip.bat getSupportedTokens ethereum
ccip.bat list-networks
ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress
```

#### ✅ 方法4: 直接使用npx
```powershell
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
npx tsx src\index.ts list-networks
npx tsx src\index.ts send ethereum 0xRouter polygon --receiver 0xAddress --yaml-config ..\..\..\config\rpc.yaml
```

## 📋 实际使用示例

### 1. 查看所有支持的网络
```powershell
# PowerShell
PS> .\ccip.bat list-networks

# 输出示例:
🌐 Supported Networks
====================

📍 eth (Chain ID: 1)
   Aliases: ethereum, mainnet, ethereum-mainnet

📍 polygon (Chain ID: 137)
   Aliases: matic, polygon-mainnet

📍 sepolia (Chain ID: 11155111)
   Aliases: eth-sepolia, ethereum-testnet-sepolia
```

### 2. 查看特定网络的支持代币
```powershell
# 使用网络名称
PS> .\ccip.bat getSupportedTokens ethereum
PS> .\ccip.bat getSupportedTokens polygon
PS> .\ccip.bat getSupportedTokens sepolia

# 使用链ID (仍然支持)
PS> .\ccip.bat getSupportedTokens 1
PS> .\ccip.bat getSupportedTokens 137
```

### 3. 发送跨链消息
```powershell
# 基本跨链消息
PS> .\ccip.bat send ethereum ****************************************** polygon --receiver 0xYourReceiverAddress

# 测试网示例
PS> .\ccip.bat send sepolia ****************************************** fuji --receiver 0xYourReceiverAddress

# 带数据的消息
PS> .\ccip.bat send ethereum 0xRouter arbitrum --receiver 0xAddress --data "Hello Cross-Chain World"

# 代币转移
PS> .\ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --transfer-tokens 0xTokenAddress=1.5
```

### 4. 查看消息状态
```powershell
PS> .\ccip.bat show 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
```

### 5. 手动执行失败的消息
```powershell
PS> .\ccip.bat manualExec 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
```

## 🌐 网络名称速查表

### 主网络
```powershell
# Ethereum Mainnet (Chain ID: 1)
.\ccip.bat getSupportedTokens ethereum
.\ccip.bat getSupportedTokens eth
.\ccip.bat getSupportedTokens mainnet

# Polygon Mainnet (Chain ID: 137)
.\ccip.bat getSupportedTokens polygon
.\ccip.bat getSupportedTokens matic

# BSC Mainnet (Chain ID: 56)
.\ccip.bat getSupportedTokens bsc
.\ccip.bat getSupportedTokens bnb
.\ccip.bat getSupportedTokens binance

# Avalanche Mainnet (Chain ID: 43114)
.\ccip.bat getSupportedTokens avalanche
.\ccip.bat getSupportedTokens avax

# Arbitrum Mainnet (Chain ID: 42161)
.\ccip.bat getSupportedTokens arbitrum
.\ccip.bat getSupportedTokens arb

# Optimism Mainnet (Chain ID: 10)
.\ccip.bat getSupportedTokens optimism
.\ccip.bat getSupportedTokens op

# Base Mainnet (Chain ID: 8453)
.\ccip.bat getSupportedTokens base
```

### 测试网络
```powershell
# Ethereum Sepolia (Chain ID: 11155111)
.\ccip.bat getSupportedTokens sepolia
.\ccip.bat getSupportedTokens eth-sepolia

# Avalanche Fuji (Chain ID: 43113)
.\ccip.bat getSupportedTokens fuji
.\ccip.bat getSupportedTokens avax-fuji
.\ccip.bat getSupportedTokens avalanche-fuji

# Polygon Amoy (Chain ID: 80002)
.\ccip.bat getSupportedTokens polygon-amoy
.\ccip.bat getSupportedTokens amoy
.\ccip.bat getSupportedTokens matic-amoy

# BSC Testnet (Chain ID: 97)
.\ccip.bat getSupportedTokens bsc-testnet
.\ccip.bat getSupportedTokens bnb-testnet
```

## 🛠️ 完整的工作流程示例

### 场景: 从Ethereum Sepolia发送消息到Avalanche Fuji

```powershell
# 1. 首先查看支持的网络
PS> .\ccip.bat list-networks

# 2. 查看Sepolia支持的代币
PS> .\ccip.bat getSupportedTokens sepolia

# 3. 查看Fuji支持的代币
PS> .\ccip.bat getSupportedTokens fuji

# 4. 发送跨链消息
PS> .\ccip.bat send sepolia ****************************************** fuji --receiver 0xYourAddress --data "Hello from Sepolia to Fuji"

# 5. 查看消息状态 (使用返回的交易哈希)
PS> .\ccip.bat show 0xYourTransactionHash
```

## ❌ 常见错误和解决方案

### 错误1: 找不到命令
```
ccip.bat : 无法将"ccip.bat"项识别为 cmdlet、函数、脚本文件或可运行程序的名称
```
**解决**: 使用 `.\ccip.bat` 而不是 `ccip.bat`

### 错误2: 网络名称无效
```
❌ Invalid source network: "etherem"
💡 Did you mean: ethereum, ethereum-mainnet
```
**解决**: 使用 `.\ccip.bat list-networks` 查看正确的网络名称

### 错误3: 配置文件未找到
```
❌ Configuration file not found: ..\..\..\config\rpc.yaml
```
**解决**: 确保在正确的目录运行命令，或检查配置文件路径

### 错误4: PowerShell执行策略
```
无法加载文件，因为在此系统上禁止运行脚本
```
**解决**: 
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process
```

## 💡 实用技巧

### 1. 创建PowerShell别名
```powershell
# 在PowerShell配置文件中添加
Set-Alias ccip "C:\path\to\ccip-tools-ts\ccip.bat"

# 然后可以直接使用
ccip list-networks
ccip getSupportedTokens ethereum
```

### 2. 使用Tab补全
```powershell
# 输入 .\ccip. 然后按Tab键自动补全
.\ccip.<Tab>
```

### 3. 保存常用命令
创建一个 `my-commands.txt` 文件：
```
.\ccip.bat list-networks
.\ccip.bat getSupportedTokens ethereum
.\ccip.bat send sepolia ****************************************** fuji --receiver 0xYourAddress
```

### 4. 使用详细日志调试
```powershell
.\ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --verbose
```

## 🎯 推荐的开发环境设置

### 1. 设置工作目录
```powershell
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\bridge\ccip-tools-ts
```

### 2. 验证环境
```powershell
# 检查Node.js
node --version

# 检查npm
npm --version

# 测试CCIP工具
.\ccip.bat --version
```

### 3. 日常使用
```powershell
# 查看网络
.\ccip.bat list-networks

# 快速测试
.\ccip.bat getSupportedTokens ethereum

# 发送测试消息
.\ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --data "test"
```

现在您应该能够在Windows PowerShell中正确使用CCIP工具了！记住关键是使用 `.\` 前缀。
