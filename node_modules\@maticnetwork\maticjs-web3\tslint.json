{
  "rules": {
    "array-type": [
      true,
      "array-simple"
    ],
    "arrow-return-shorthand": true,
    "ban": [
      true,
      {
        "name": "parseFloat",
        "message": "tsstyle#type-coercion"
      },
      {
        "name": "Array",
        "message": "tsstyle#array-constructor"
      }
    ],
    "ban-types": [
      true,
      [
        "Object",
        "Use {} instead."
      ],
      [
        "String",
        "Use 'string' instead."
      ],
      [
        "Number",
        "Use 'number' instead."
      ],
      [
        "Boolean",
        "Use 'boolean' instead."
      ]
    ],
    "class-name": true,
    "curly": [
      true,
      "ignore-same-line"
    ],
    // "forin": true,
    "interface-name": [
      true,
      "allow-prefix"
    ],
    "jsdoc-format": true,
    "label-position": true,
    "member-access": [
      true,
      "no-public"
    ],
    "new-parens": true,
    "no-angle-bracket-type-assertion": true,
    "no-any": false,
    "no-arg": true,
    "no-conditional-assignment": true,
    "no-construct": true,
    "no-debugger": true,
    "no-default-export": true,
    "no-duplicate-variable": true,
    "no-inferrable-types": true,
    "no-namespace": [
      true,
      "allow-declarations"
    ],
    "no-reference": true,
    "no-string-throw": true,
    "no-unused-expression": true,
    "no-var-keyword": true,
    "object-literal-shorthand": false,
    "prefer-const": true,
    "semicolon": [
      true,
      "always",
      "ignore-bound-class-methods"
    ],
    // "switch-default": true,
    "triple-equals": [
      true,
      "allow-null-check"
    ],
    "use-isnan": true,
    "variable-name": [
      true,
      "check-format",
      "ban-keywords",
      "allow-leading-underscore",
      "allow-trailing-underscore"
    ],
    "no-shadowed-variable": true,
    "naming-convention": [true,
      // this config will apply to properties AND methods, if you only need it for properties, use "property" instead of "member"
      {"type": "member", "format": "camelCase"}, // use camelCase for all members, will be inherited by protected and private
      {"type": "member", "modifiers": "protected", "trailingUnderscore": "require"}, // protected members will be REQUIRED to have a leading underscore. you can use "allow" as alternative
      {"type": "member", "modifiers": "private", "suffix": "__"} // to simply allow and not enforce double leading underscores, use "prefix": ["__", ""]
    ]
  }
}