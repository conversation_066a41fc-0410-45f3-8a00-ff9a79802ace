@echo off
echo 🧪 Basic CCIP Test
echo ==================

echo Current directory: %CD%
echo.

echo Testing direct npx command...
npx tsx src\index.ts --version
if %errorlevel% neq 0 (
    echo ❌ Version command failed
    goto :error
)
echo.

echo Testing list-networks command...
npx tsx src\index.ts list-networks
if %errorlevel% neq 0 (
    echo ❌ List networks command failed
    goto :error
)
echo.

echo Testing network name resolution...
echo Testing getSupportedTokens with "ethereum":
npx tsx src\index.ts getSupportedTokens ethereum --yaml-config ..\..\..\config\rpc.yaml
if %errorlevel% neq 0 (
    echo ❌ Network name resolution failed
    goto :error
)
echo.

echo ✅ All tests passed!
goto :end

:error
echo ❌ Test failed!
exit /b 1

:end
echo ✨ Basic test completed!
